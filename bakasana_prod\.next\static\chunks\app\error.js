/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/error"],{

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e) {\n    var t, f, n = \"\";\n    if (\"string\" == typeof e || \"number\" == typeof e) n += e;\n    else if (\"object\" == typeof e) if (Array.isArray(e)) {\n        var o = e.length;\n        for(t = 0; t < o; t++)e[t] && (f = r(e[t])) && (n && (n += \" \"), n += f);\n    } else for(f in e)e[f] && (n && (n += \" \"), n += f);\n    return n;\n}\nfunction clsx() {\n    for(var e, t, f = 0, n = \"\", o = arguments.length; f < o; f++)(e = arguments[f]) && (t = r(e)) && (n && (n += \" \"), n += t);\n    return n;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxTQUFTQSxFQUFFQyxDQUFDO0lBQUUsSUFBSUMsR0FBRUMsR0FBRUMsSUFBRTtJQUFHLElBQUcsWUFBVSxPQUFPSCxLQUFHLFlBQVUsT0FBT0EsR0FBRUcsS0FBR0g7U0FBTyxJQUFHLFlBQVUsT0FBT0EsR0FBRSxJQUFHSSxNQUFNQyxPQUFPLENBQUNMLElBQUc7UUFBQyxJQUFJTSxJQUFFTixFQUFFTyxNQUFNO1FBQUMsSUFBSU4sSUFBRSxHQUFFQSxJQUFFSyxHQUFFTCxJQUFJRCxDQUFDLENBQUNDLEVBQUUsSUFBR0MsQ0FBQUEsSUFBRUgsRUFBRUMsQ0FBQyxDQUFDQyxFQUFFLE1BQUtFLENBQUFBLEtBQUlBLENBQUFBLEtBQUcsR0FBRSxHQUFHQSxLQUFHRCxDQUFBQTtJQUFFLE9BQU0sSUFBSUEsS0FBS0YsRUFBRUEsQ0FBQyxDQUFDRSxFQUFFLElBQUdDLENBQUFBLEtBQUlBLENBQUFBLEtBQUcsR0FBRSxHQUFHQSxLQUFHRCxDQUFBQTtJQUFHLE9BQU9DO0FBQUM7QUFBUSxTQUFTSztJQUFPLElBQUksSUFBSVIsR0FBRUMsR0FBRUMsSUFBRSxHQUFFQyxJQUFFLElBQUdHLElBQUVHLFVBQVVGLE1BQU0sRUFBQ0wsSUFBRUksR0FBRUosSUFBSSxDQUFDRixJQUFFUyxTQUFTLENBQUNQLEVBQUUsS0FBSUQsQ0FBQUEsSUFBRUYsRUFBRUMsRUFBQyxLQUFLRyxDQUFBQSxLQUFJQSxDQUFBQSxLQUFHLEdBQUUsR0FBR0EsS0FBR0YsQ0FBQUE7SUFBRyxPQUFPRTtBQUFDO0FBQUMsaUVBQWVLLElBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbInIiLCJlIiwidCIsImYiLCJuIiwiQXJyYXkiLCJpc0FycmF5IiwibyIsImxlbmd0aCIsImNsc3giLCJhcmd1bWVudHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.jsx */ \"(app-pages-browser)/./src/app/error.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZGF2aWQlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVrdHklNUMlNUNiYWthc2FuYV9wcm9kJTVDJTVDYmFrYXNhbmFfcHJvZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUEySCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZGF2aWRcXFxcRGVza3RvcFxcXFxQcm9qZWt0eVxcXFxiYWthc2FuYV9wcm9kXFxcXGJha2FzYW5hX3Byb2RcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci5qc3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    if (onNavigate) {\n        let isDefaultPrevented = false;\n        onNavigate({\n            preventDefault: ()=>{\n                isDefaultPrevented = true;\n            }\n        });\n        if (isDefaultPrevented) {\n            return;\n        }\n    }\n    _react.default.startTransition(()=>{\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    });\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null || prefetchProp === 'auto' ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'prefetch') {\n                if (props[key] != null && valType !== 'boolean' && props[key] !== 'auto') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean | \"auto\"`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L3VzZS1tZXJnZWQtcmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBU2dCQTs7O2VBQUFBOzs7bUNBVDhCO0FBU3ZDLFNBQVNBLGFBQ2RDLElBQW1CLEVBQ25CQyxJQUFtQjtJQUVuQixNQUFNQyxXQUFXQyxDQUFBQSxHQUFBQSxPQUFBQSxNQUFBQSxFQUE0QjtJQUM3QyxNQUFNQyxXQUFXRCxDQUFBQSxHQUFBQSxPQUFBQSxNQUFBQSxFQUE0QjtJQUU3QyxtRkFBbUY7SUFDbkYseUVBQXlFO0lBQ3pFLGlHQUFpRztJQUNqRyw4RkFBOEY7SUFDOUYsZ0RBQWdEO0lBQ2hELG1HQUFtRztJQUNuRyx3RkFBd0Y7SUFDeEYsT0FBT0UsQ0FBQUEsR0FBQUEsT0FBQUEsV0FBQUEsRUFDTCxDQUFDQztRQUNDLElBQUlBLFlBQVksTUFBTTtZQUNwQixNQUFNQyxhQUFhTCxTQUFTSSxPQUFPO1lBQ25DLElBQUlDLFlBQVk7Z0JBQ2RMLFNBQVNJLE9BQU8sR0FBRztnQkFDbkJDO1lBQ0Y7WUFDQSxNQUFNQyxhQUFhSixTQUFTRSxPQUFPO1lBQ25DLElBQUlFLFlBQVk7Z0JBQ2RKLFNBQVNFLE9BQU8sR0FBRztnQkFDbkJFO1lBQ0Y7UUFDRixPQUFPO1lBQ0wsSUFBSVIsTUFBTTtnQkFDUkUsU0FBU0ksT0FBTyxHQUFHRyxTQUFTVCxNQUFNTTtZQUNwQztZQUNBLElBQUlMLE1BQU07Z0JBQ1JHLFNBQVNFLE9BQU8sR0FBR0csU0FBU1IsTUFBTUs7WUFDcEM7UUFDRjtJQUNGLEdBQ0E7UUFBQ047UUFBTUM7S0FBSztBQUVoQjtBQUVBLFNBQVNRLFNBQ1BULElBQWdDLEVBQ2hDTSxPQUFpQjtJQUVqQixJQUFJLE9BQU9OLFNBQVMsWUFBWTtRQUM5QixNQUFNVSxVQUFVVixLQUFLTTtRQUNyQixJQUFJLE9BQU9JLFlBQVksWUFBWTtZQUNqQyxPQUFPQTtRQUNULE9BQU87WUFDTCxPQUFPLElBQU1WLEtBQUs7UUFDcEI7SUFDRixPQUFPO1FBQ0xBLEtBQUtNLE9BQU8sR0FBR0E7UUFDZixPQUFPO1lBQ0xOLEtBQUtNLE9BQU8sR0FBRztRQUNqQjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxzcmNcXGNsaWVudFxcdXNlLW1lcmdlZC1yZWYudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVJlZiwgdHlwZSBSZWYgfSBmcm9tICdyZWFjdCdcblxuLy8gVGhpcyBpcyBhIGNvbXBhdGliaWxpdHkgaG9vayB0byBzdXBwb3J0IFJlYWN0IDE4IGFuZCAxOSByZWZzLlxuLy8gSW4gMTksIGEgY2xlYW51cCBmdW5jdGlvbiBmcm9tIHJlZnMgbWF5IGJlIHJldHVybmVkLlxuLy8gSW4gMTgsIHJldHVybmluZyBhIGNsZWFudXAgZnVuY3Rpb24gY3JlYXRlcyBhIHdhcm5pbmcuXG4vLyBTaW5jZSB3ZSB0YWtlIHVzZXJzcGFjZSByZWZzLCB3ZSBkb24ndCBrbm93IGFoZWFkIG9mIHRpbWUgaWYgYSBjbGVhbnVwIGZ1bmN0aW9uIHdpbGwgYmUgcmV0dXJuZWQuXG4vLyBUaGlzIGltcGxlbWVudHMgY2xlYW51cCBmdW5jdGlvbnMgd2l0aCB0aGUgb2xkIGJlaGF2aW9yIGluIDE4LlxuLy8gV2Uga25vdyByZWZzIGFyZSBhbHdheXMgY2FsbGVkIGFsdGVybmF0aW5nIHdpdGggYG51bGxgIGFuZCB0aGVuIGBUYC5cbi8vIFNvIGEgY2FsbCB3aXRoIGBudWxsYCBtZWFucyB3ZSBuZWVkIHRvIGNhbGwgdGhlIHByZXZpb3VzIGNsZWFudXAgZnVuY3Rpb25zLlxuZXhwb3J0IGZ1bmN0aW9uIHVzZU1lcmdlZFJlZjxURWxlbWVudD4oXG4gIHJlZkE6IFJlZjxURWxlbWVudD4sXG4gIHJlZkI6IFJlZjxURWxlbWVudD5cbik6IFJlZjxURWxlbWVudD4ge1xuICBjb25zdCBjbGVhbnVwQSA9IHVzZVJlZjwoKCkgPT4gdm9pZCkgfCBudWxsPihudWxsKVxuICBjb25zdCBjbGVhbnVwQiA9IHVzZVJlZjwoKCkgPT4gdm9pZCkgfCBudWxsPihudWxsKVxuXG4gIC8vIE5PVEU6IEluIHRoZW9yeSwgd2UgY291bGQgc2tpcCB0aGUgd3JhcHBpbmcgaWYgb25seSBvbmUgb2YgdGhlIHJlZnMgaXMgbm9uLW51bGwuXG4gIC8vICh0aGlzIGhhcHBlbnMgb2Z0ZW4gaWYgdGhlIHVzZXIgZG9lc24ndCBwYXNzIGEgcmVmIHRvIExpbmsvRm9ybS9JbWFnZSlcbiAgLy8gQnV0IHRoaXMgY2FuIGNhdXNlIHVzIHRvIGxlYWsgYSBjbGVhbnVwLXJlZiBpbnRvIHVzZXIgY29kZSAoZS5nLiB2aWEgYDxMaW5rIGxlZ2FjeUJlaGF2aW9yPmApLFxuICAvLyBhbmQgdGhlIHVzZXIgbWlnaHQgcGFzcyB0aGF0IHJlZiBpbnRvIHJlZi1tZXJnaW5nIGxpYnJhcnkgdGhhdCBkb2Vzbid0IHN1cHBvcnQgY2xlYW51cCByZWZzXG4gIC8vIChiZWNhdXNlIGl0IGhhc24ndCBiZWVuIHVwZGF0ZWQgZm9yIFJlYWN0IDE5KVxuICAvLyB3aGljaCBjYW4gdGhlbiBjYXVzZSB0aGluZ3MgdG8gYmxvdyB1cCwgYmVjYXVzZSBhIGNsZWFudXAtcmV0dXJuaW5nIHJlZiBnZXRzIGNhbGxlZCB3aXRoIGBudWxsYC5cbiAgLy8gU28gaW4gcHJhY3RpY2UsIGl0J3Mgc2FmZXIgdG8gYmUgZGVmZW5zaXZlIGFuZCBhbHdheXMgd3JhcCB0aGUgcmVmLCBldmVuIG9uIFJlYWN0IDE5LlxuICByZXR1cm4gdXNlQ2FsbGJhY2soXG4gICAgKGN1cnJlbnQ6IFRFbGVtZW50IHwgbnVsbCk6IHZvaWQgPT4ge1xuICAgICAgaWYgKGN1cnJlbnQgPT09IG51bGwpIHtcbiAgICAgICAgY29uc3QgY2xlYW51cEZuQSA9IGNsZWFudXBBLmN1cnJlbnRcbiAgICAgICAgaWYgKGNsZWFudXBGbkEpIHtcbiAgICAgICAgICBjbGVhbnVwQS5jdXJyZW50ID0gbnVsbFxuICAgICAgICAgIGNsZWFudXBGbkEoKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNsZWFudXBGbkIgPSBjbGVhbnVwQi5jdXJyZW50XG4gICAgICAgIGlmIChjbGVhbnVwRm5CKSB7XG4gICAgICAgICAgY2xlYW51cEIuY3VycmVudCA9IG51bGxcbiAgICAgICAgICBjbGVhbnVwRm5CKClcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKHJlZkEpIHtcbiAgICAgICAgICBjbGVhbnVwQS5jdXJyZW50ID0gYXBwbHlSZWYocmVmQSwgY3VycmVudClcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVmQikge1xuICAgICAgICAgIGNsZWFudXBCLmN1cnJlbnQgPSBhcHBseVJlZihyZWZCLCBjdXJyZW50KVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICBbcmVmQSwgcmVmQl1cbiAgKVxufVxuXG5mdW5jdGlvbiBhcHBseVJlZjxURWxlbWVudD4oXG4gIHJlZkE6IE5vbk51bGxhYmxlPFJlZjxURWxlbWVudD4+LFxuICBjdXJyZW50OiBURWxlbWVudFxuKSB7XG4gIGlmICh0eXBlb2YgcmVmQSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGNvbnN0IGNsZWFudXAgPSByZWZBKGN1cnJlbnQpXG4gICAgaWYgKHR5cGVvZiBjbGVhbnVwID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICByZXR1cm4gY2xlYW51cFxuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gKCkgPT4gcmVmQShudWxsKVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByZWZBLmN1cnJlbnQgPSBjdXJyZW50XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHJlZkEuY3VycmVudCA9IG51bGxcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VNZXJnZWRSZWYiLCJyZWZBIiwicmVmQiIsImNsZWFudXBBIiwidXNlUmVmIiwiY2xlYW51cEIiLCJ1c2VDYWxsYmFjayIsImN1cnJlbnQiLCJjbGVhbnVwRm5BIiwiY2xlYW51cEZuQiIsImFwcGx5UmVmIiwiY2xlYW51cCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3JjXFxzaGFyZWRcXGxpYlxccm91dGVyXFx1dGlsc1xcaXMtbG9jYWwtdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQWJzb2x1dGVVcmwsIGdldExvY2F0aW9uT3JpZ2luIH0gZnJvbSAnLi4vLi4vdXRpbHMnXG5pbXBvcnQgeyBoYXNCYXNlUGF0aCB9IGZyb20gJy4uLy4uLy4uLy4uL2NsaWVudC9oYXMtYmFzZS1wYXRoJ1xuXG4vKipcbiAqIERldGVjdHMgd2hldGhlciBhIGdpdmVuIHVybCBpcyByb3V0YWJsZSBieSB0aGUgTmV4dC5qcyByb3V0ZXIgKGJyb3dzZXIgb25seSkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0xvY2FsVVJMKHVybDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIHByZXZlbnQgYSBoeWRyYXRpb24gbWlzbWF0Y2ggb24gaHJlZiBmb3IgdXJsIHdpdGggYW5jaG9yIHJlZnNcbiAgaWYgKCFpc0Fic29sdXRlVXJsKHVybCkpIHJldHVybiB0cnVlXG4gIHRyeSB7XG4gICAgLy8gYWJzb2x1dGUgdXJscyBjYW4gYmUgbG9jYWwgaWYgdGhleSBhcmUgb24gdGhlIHNhbWUgb3JpZ2luXG4gICAgY29uc3QgbG9jYXRpb25PcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gICAgY29uc3QgcmVzb2x2ZWQgPSBuZXcgVVJMKHVybCwgbG9jYXRpb25PcmlnaW4pXG4gICAgcmV0dXJuIHJlc29sdmVkLm9yaWdpbiA9PT0gbG9jYXRpb25PcmlnaW4gJiYgaGFzQmFzZVBhdGgocmVzb2x2ZWQucGF0aG5hbWUpXG4gIH0gY2F0Y2ggKF8pIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbImlzTG9jYWxVUkwiLCJ1cmwiLCJpc0Fic29sdXRlVXJsIiwibG9jYXRpb25PcmlnaW4iLCJnZXRMb2NhdGlvbk9yaWdpbiIsInJlc29sdmVkIiwiVVJMIiwib3JpZ2luIiwiaGFzQmFzZVBhdGgiLCJwYXRobmFtZSIsIl8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvcXVlcnlzdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZ0RnQkEsTUFBTTtlQUFOQTs7SUE5Q0FDLHNCQUFzQjtlQUF0QkE7O0lBZ0NBQyxzQkFBc0I7ZUFBdEJBOzs7QUFoQ1QsU0FBU0QsdUJBQ2RFLFlBQTZCO0lBRTdCLE1BQU1DLFFBQXdCLENBQUM7SUFDL0IsS0FBSyxNQUFNLENBQUNDLEtBQUtDLE1BQU0sSUFBSUgsYUFBYUksT0FBTyxHQUFJO1FBQ2pELE1BQU1DLFdBQVdKLEtBQUssQ0FBQ0MsSUFBSTtRQUMzQixJQUFJLE9BQU9HLGFBQWEsYUFBYTtZQUNuQ0osS0FBSyxDQUFDQyxJQUFJLEdBQUdDO1FBQ2YsT0FBTyxJQUFJRyxNQUFNQyxPQUFPLENBQUNGLFdBQVc7WUFDbENBLFNBQVNHLElBQUksQ0FBQ0w7UUFDaEIsT0FBTztZQUNMRixLQUFLLENBQUNDLElBQUksR0FBRztnQkFBQ0c7Z0JBQVVGO2FBQU07UUFDaEM7SUFDRjtJQUNBLE9BQU9GO0FBQ1Q7QUFFQSxTQUFTUSx1QkFBdUJDLEtBQWM7SUFDNUMsSUFBSSxPQUFPQSxVQUFVLFVBQVU7UUFDN0IsT0FBT0E7SUFDVDtJQUVBLElBQ0csT0FBT0EsVUFBVSxZQUFZLENBQUNDLE1BQU1ELFVBQ3JDLE9BQU9BLFVBQVUsV0FDakI7UUFDQSxPQUFPRSxPQUFPRjtJQUNoQixPQUFPO1FBQ0wsT0FBTztJQUNUO0FBQ0Y7QUFFTyxTQUFTWCx1QkFBdUJFLEtBQXFCO0lBQzFELE1BQU1ELGVBQWUsSUFBSWE7SUFDekIsS0FBSyxNQUFNLENBQUNYLEtBQUtDLE1BQU0sSUFBSVcsT0FBT1YsT0FBTyxDQUFDSCxPQUFRO1FBQ2hELElBQUlLLE1BQU1DLE9BQU8sQ0FBQ0osUUFBUTtZQUN4QixLQUFLLE1BQU1ZLFFBQVFaLE1BQU87Z0JBQ3hCSCxhQUFhZ0IsTUFBTSxDQUFDZCxLQUFLTyx1QkFBdUJNO1lBQ2xEO1FBQ0YsT0FBTztZQUNMZixhQUFhaUIsR0FBRyxDQUFDZixLQUFLTyx1QkFBdUJOO1FBQy9DO0lBQ0Y7SUFDQSxPQUFPSDtBQUNUO0FBRU8sU0FBU0gsT0FDZHFCLE1BQXVCO0lBQ3ZCLGlDQUFHQyxtQkFBSDtRQUFHQSxnQkFBQUEsQ0FBSCwyQkFBc0M7O0lBRXRDLEtBQUssTUFBTW5CLGdCQUFnQm1CLGlCQUFrQjtRQUMzQyxLQUFLLE1BQU1qQixPQUFPRixhQUFhb0IsSUFBSSxHQUFJO1lBQ3JDRixPQUFPRyxNQUFNLENBQUNuQjtRQUNoQjtRQUVBLEtBQUssTUFBTSxDQUFDQSxLQUFLQyxNQUFNLElBQUlILGFBQWFJLE9BQU8sR0FBSTtZQUNqRGMsT0FBT0YsTUFBTSxDQUFDZCxLQUFLQztRQUNyQjtJQUNGO0lBRUEsT0FBT2U7QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzcmNcXHNoYXJlZFxcbGliXFxyb3V0ZXJcXHV0aWxzXFxxdWVyeXN0cmluZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IFBhcnNlZFVybFF1ZXJ5IH0gZnJvbSAncXVlcnlzdHJpbmcnXG5cbmV4cG9ydCBmdW5jdGlvbiBzZWFyY2hQYXJhbXNUb1VybFF1ZXJ5KFxuICBzZWFyY2hQYXJhbXM6IFVSTFNlYXJjaFBhcmFtc1xuKTogUGFyc2VkVXJsUXVlcnkge1xuICBjb25zdCBxdWVyeTogUGFyc2VkVXJsUXVlcnkgPSB7fVxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBzZWFyY2hQYXJhbXMuZW50cmllcygpKSB7XG4gICAgY29uc3QgZXhpc3RpbmcgPSBxdWVyeVtrZXldXG4gICAgaWYgKHR5cGVvZiBleGlzdGluZyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHF1ZXJ5W2tleV0gPSB2YWx1ZVxuICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShleGlzdGluZykpIHtcbiAgICAgIGV4aXN0aW5nLnB1c2godmFsdWUpXG4gICAgfSBlbHNlIHtcbiAgICAgIHF1ZXJ5W2tleV0gPSBbZXhpc3RpbmcsIHZhbHVlXVxuICAgIH1cbiAgfVxuICByZXR1cm4gcXVlcnlcbn1cblxuZnVuY3Rpb24gc3RyaW5naWZ5VXJsUXVlcnlQYXJhbShwYXJhbTogdW5rbm93bik6IHN0cmluZyB7XG4gIGlmICh0eXBlb2YgcGFyYW0gPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIHBhcmFtXG4gIH1cblxuICBpZiAoXG4gICAgKHR5cGVvZiBwYXJhbSA9PT0gJ251bWJlcicgJiYgIWlzTmFOKHBhcmFtKSkgfHxcbiAgICB0eXBlb2YgcGFyYW0gPT09ICdib29sZWFuJ1xuICApIHtcbiAgICByZXR1cm4gU3RyaW5nKHBhcmFtKVxuICB9IGVsc2Uge1xuICAgIHJldHVybiAnJ1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1cmxRdWVyeVRvU2VhcmNoUGFyYW1zKHF1ZXJ5OiBQYXJzZWRVcmxRdWVyeSk6IFVSTFNlYXJjaFBhcmFtcyB7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKVxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhxdWVyeSkpIHtcbiAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiB2YWx1ZSkge1xuICAgICAgICBzZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgc3RyaW5naWZ5VXJsUXVlcnlQYXJhbShpdGVtKSlcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgc2VhcmNoUGFyYW1zLnNldChrZXksIHN0cmluZ2lmeVVybFF1ZXJ5UGFyYW0odmFsdWUpKVxuICAgIH1cbiAgfVxuICByZXR1cm4gc2VhcmNoUGFyYW1zXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhc3NpZ24oXG4gIHRhcmdldDogVVJMU2VhcmNoUGFyYW1zLFxuICAuLi5zZWFyY2hQYXJhbXNMaXN0OiBVUkxTZWFyY2hQYXJhbXNbXVxuKTogVVJMU2VhcmNoUGFyYW1zIHtcbiAgZm9yIChjb25zdCBzZWFyY2hQYXJhbXMgb2Ygc2VhcmNoUGFyYW1zTGlzdCkge1xuICAgIGZvciAoY29uc3Qga2V5IG9mIHNlYXJjaFBhcmFtcy5rZXlzKCkpIHtcbiAgICAgIHRhcmdldC5kZWxldGUoa2V5KVxuICAgIH1cblxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIHNlYXJjaFBhcmFtcy5lbnRyaWVzKCkpIHtcbiAgICAgIHRhcmdldC5hcHBlbmQoa2V5LCB2YWx1ZSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0XG59XG4iXSwibmFtZXMiOlsiYXNzaWduIiwic2VhcmNoUGFyYW1zVG9VcmxRdWVyeSIsInVybFF1ZXJ5VG9TZWFyY2hQYXJhbXMiLCJzZWFyY2hQYXJhbXMiLCJxdWVyeSIsImtleSIsInZhbHVlIiwiZW50cmllcyIsImV4aXN0aW5nIiwiQXJyYXkiLCJpc0FycmF5IiwicHVzaCIsInN0cmluZ2lmeVVybFF1ZXJ5UGFyYW0iLCJwYXJhbSIsImlzTmFOIiwiU3RyaW5nIiwiVVJMU2VhcmNoUGFyYW1zIiwiT2JqZWN0IiwiaXRlbSIsImFwcGVuZCIsInNldCIsInRhcmdldCIsInNlYXJjaFBhcmFtc0xpc3QiLCJrZXlzIiwiZGVsZXRlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXHNyY1xcc2hhcmVkXFxsaWJcXHV0aWxzXFxlcnJvci1vbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlcnJvck9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3QgZXJyb3JzID0gbmV3IFNldDxzdHJpbmc+KClcbiAgZXJyb3JPbmNlID0gKG1zZzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFlcnJvcnMuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IobXNnKVxuICAgIH1cbiAgICBlcnJvcnMuYWRkKG1zZylcbiAgfVxufVxuXG5leHBvcnQgeyBlcnJvck9uY2UgfVxuIl0sIm5hbWVzIjpbImVycm9yT25jZSIsIl8iLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJlcnJvcnMiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwiZXJyb3IiLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = (config)=>{\n    const classMap = createClassMap(config);\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config;\n    const getClassGroupId = (className)=>{\n        const classParts = className.split(CLASS_PART_SEPARATOR);\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift();\n        }\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n    };\n    const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier)=>{\n        const conflicts = conflictingClassGroups[classGroupId] || [];\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [\n                ...conflicts,\n                ...conflictingClassGroupModifiers[classGroupId]\n            ];\n        }\n        return conflicts;\n    };\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds\n    };\n};\nconst getGroupRecursive = (classParts, classPartObject)=>{\n    var _classPartObject_validators_find;\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId;\n    }\n    const currentClassPart = classParts[0];\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n    const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart;\n    }\n    if (classPartObject.validators.length === 0) {\n        return undefined;\n    }\n    const classRest = classParts.join(CLASS_PART_SEPARATOR);\n    return (_classPartObject_validators_find = classPartObject.validators.find((param)=>{\n        let { validator } = param;\n        return validator(classRest);\n    })) === null || _classPartObject_validators_find === void 0 ? void 0 : _classPartObject_validators_find.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = (className)=>{\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n        const property = arbitraryPropertyClassName === null || arbitraryPropertyClassName === void 0 ? void 0 : arbitraryPropertyClassName.substring(0, arbitraryPropertyClassName.indexOf(':'));\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property;\n        }\n    }\n};\n/**\n * Exported for testing only\n */ const createClassMap = (config)=>{\n    const { theme, classGroups } = config;\n    const classMap = {\n        nextPart: new Map(),\n        validators: []\n    };\n    for(const classGroupId in classGroups){\n        processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n    }\n    return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme)=>{\n    classGroup.forEach((classDefinition)=>{\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n            classPartObjectToEdit.classGroupId = classGroupId;\n            return;\n        }\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n                return;\n            }\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId\n            });\n            return;\n        }\n        Object.entries(classDefinition).forEach((param)=>{\n            let [key, classGroup] = param;\n            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n        });\n    });\n};\nconst getPart = (classPartObject, path)=>{\n    let currentClassPartObject = classPartObject;\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart)=>{\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: []\n            });\n        }\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n    });\n    return currentClassPartObject;\n};\nconst isThemeGetter = (func)=>func.isThemeGetter;\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = (maxCacheSize)=>{\n    if (maxCacheSize < 1) {\n        return {\n            get: ()=>undefined,\n            set: ()=>{}\n        };\n    }\n    let cacheSize = 0;\n    let cache = new Map();\n    let previousCache = new Map();\n    const update = (key, value)=>{\n        cache.set(key, value);\n        cacheSize++;\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0;\n            previousCache = cache;\n            cache = new Map();\n        }\n    };\n    return {\n        get (key) {\n            let value = cache.get(key);\n            if (value !== undefined) {\n                return value;\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value);\n                return value;\n            }\n        },\n        set (key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value);\n            } else {\n                update(key, value);\n            }\n        }\n    };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = (config)=>{\n    const { prefix, experimentalParseClassName } = config;\n    /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */ let parseClassName = (className)=>{\n        const modifiers = [];\n        let bracketDepth = 0;\n        let parenDepth = 0;\n        let modifierStart = 0;\n        let postfixModifierPosition;\n        for(let index = 0; index < className.length; index++){\n            let currentCharacter = className[index];\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index));\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n                    continue;\n                }\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index;\n                    continue;\n                }\n            }\n            if (currentCharacter === '[') {\n                bracketDepth++;\n            } else if (currentCharacter === ']') {\n                bracketDepth--;\n            } else if (currentCharacter === '(') {\n                parenDepth++;\n            } else if (currentCharacter === ')') {\n                parenDepth--;\n            }\n        }\n        const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n        const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition\n        };\n    };\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR;\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n                isExternal: true,\n                modifiers: [],\n                hasImportantModifier: false,\n                baseClassName: className,\n                maybePostfixModifierPosition: undefined\n            };\n    }\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>experimentalParseClassName({\n                className,\n                parseClassName: parseClassNameOriginal\n            });\n    }\n    return parseClassName;\n};\nconst stripImportantModifier = (baseClassName)=>{\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1);\n    }\n    /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */ if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1);\n    }\n    return baseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */ const createSortModifiers = (config)=>{\n    const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map((modifier)=>[\n            modifier,\n            true\n        ]));\n    const sortModifiers = (modifiers)=>{\n        if (modifiers.length <= 1) {\n            return modifiers;\n        }\n        const sortedModifiers = [];\n        let unsortedModifiers = [];\n        modifiers.forEach((modifier)=>{\n            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n                unsortedModifiers = [];\n            } else {\n                unsortedModifiers.push(modifier);\n            }\n        });\n        sortedModifiers.push(...unsortedModifiers.sort());\n        return sortedModifiers;\n    };\n    return sortModifiers;\n};\nconst createConfigUtils = (config)=>({\n        cache: createLruCache(config.cacheSize),\n        parseClassName: createParseClassName(config),\n        sortModifiers: createSortModifiers(config),\n        ...createClassGroupUtils(config)\n    });\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils)=>{\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } = configUtils;\n    /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */ const classGroupsInConflict = [];\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n    let result = '';\n    for(let index = classNames.length - 1; index >= 0; index -= 1){\n        const originalClassName = classNames[index];\n        const { isExternal, modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } = parseClassName(originalClassName);\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? ' ' + result : result);\n            continue;\n        }\n        let hasPostfixModifier = !!maybePostfixModifierPosition;\n        let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result);\n                continue;\n            }\n            classGroupId = getClassGroupId(baseClassName);\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result);\n                continue;\n            }\n            hasPostfixModifier = false;\n        }\n        const variantModifier = sortModifiers(modifiers).join(':');\n        const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n        const classId = modifierId + classGroupId;\n        if (classGroupsInConflict.includes(classId)) {\n            continue;\n        }\n        classGroupsInConflict.push(classId);\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n        for(let i = 0; i < conflictGroups.length; ++i){\n            const group = conflictGroups[i];\n            classGroupsInConflict.push(modifierId + group);\n        }\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n    }\n    return result;\n};\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */ function twJoin() {\n    let index = 0;\n    let argument;\n    let resolvedValue;\n    let string = '';\n    while(index < arguments.length){\n        if (argument = arguments[index++]) {\n            if (resolvedValue = toValue(argument)) {\n                string && (string += ' ');\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nconst toValue = (mix)=>{\n    if (typeof mix === 'string') {\n        return mix;\n    }\n    let resolvedValue;\n    let string = '';\n    for(let k = 0; k < mix.length; k++){\n        if (mix[k]) {\n            if (resolvedValue = toValue(mix[k])) {\n                string && (string += ' ');\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n};\nfunction createTailwindMerge(createConfigFirst) {\n    for(var _len = arguments.length, createConfigRest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        createConfigRest[_key - 1] = arguments[_key];\n    }\n    let configUtils;\n    let cacheGet;\n    let cacheSet;\n    let functionToCall = initTailwindMerge;\n    function initTailwindMerge(classList) {\n        const config = createConfigRest.reduce((previousConfig, createConfigCurrent)=>createConfigCurrent(previousConfig), createConfigFirst());\n        configUtils = createConfigUtils(config);\n        cacheGet = configUtils.cache.get;\n        cacheSet = configUtils.cache.set;\n        functionToCall = tailwindMerge;\n        return tailwindMerge(classList);\n    }\n    function tailwindMerge(classList) {\n        const cachedResult = cacheGet(classList);\n        if (cachedResult) {\n            return cachedResult;\n        }\n        const result = mergeClassList(classList, configUtils);\n        cacheSet(classList, result);\n        return result;\n    }\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments));\n    };\n}\nconst fromTheme = (key)=>{\n    const themeGetter = (theme)=>theme[key] || [];\n    themeGetter.isThemeGetter = true;\n    return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = (value)=>fractionRegex.test(value);\nconst isNumber = (value)=>!!value && !Number.isNaN(Number(value));\nconst isInteger = (value)=>!!value && Number.isInteger(Number(value));\nconst isPercent = (value)=>value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = (value)=>tshirtUnitRegex.test(value);\nconst isAny = ()=>true;\nconst isLengthOnly = (value)=>// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = ()=>false;\nconst isShadow = (value)=>shadowRegex.test(value);\nconst isImage = (value)=>imageRegex.test(value);\nconst isAnyNonArbitrary = (value)=>!isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = (value)=>getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = (value)=>arbitraryValueRegex.test(value);\nconst isArbitraryLength = (value)=>getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = (value)=>getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = (value)=>getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = (value)=>getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = (value)=>getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = (value)=>arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = (value)=>getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = (value)=>getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = (value)=>getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = (value)=>getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = (value)=>getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = (value)=>getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue)=>{\n    const result = arbitraryValueRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return testValue(result[2]);\n    }\n    return false;\n};\nconst getIsArbitraryVariable = function(value, testLabel) {\n    let shouldMatchNoLabel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    const result = arbitraryVariableRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return shouldMatchNoLabel;\n    }\n    return false;\n};\n// Labels\nconst isLabelPosition = (label)=>label === 'position' || label === 'percentage';\nconst isLabelImage = (label)=>label === 'image' || label === 'url';\nconst isLabelSize = (label)=>label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = (label)=>label === 'length';\nconst isLabelNumber = (label)=>label === 'number';\nconst isLabelFamilyName = (label)=>label === 'family-name';\nconst isLabelShadow = (label)=>label === 'shadow';\nconst validators = /*#__PURE__*/ Object.defineProperty({\n    __proto__: null,\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize\n}, Symbol.toStringTag, {\n    value: 'Module'\n});\nconst getDefaultConfig = ()=>{\n    /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */ /***/ const themeColor = fromTheme('color');\n    const themeFont = fromTheme('font');\n    const themeText = fromTheme('text');\n    const themeFontWeight = fromTheme('font-weight');\n    const themeTracking = fromTheme('tracking');\n    const themeLeading = fromTheme('leading');\n    const themeBreakpoint = fromTheme('breakpoint');\n    const themeContainer = fromTheme('container');\n    const themeSpacing = fromTheme('spacing');\n    const themeRadius = fromTheme('radius');\n    const themeShadow = fromTheme('shadow');\n    const themeInsetShadow = fromTheme('inset-shadow');\n    const themeTextShadow = fromTheme('text-shadow');\n    const themeDropShadow = fromTheme('drop-shadow');\n    const themeBlur = fromTheme('blur');\n    const themePerspective = fromTheme('perspective');\n    const themeAspect = fromTheme('aspect');\n    const themeEase = fromTheme('ease');\n    const themeAnimate = fromTheme('animate');\n    /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */ /***/ const scaleBreak = ()=>[\n            'auto',\n            'avoid',\n            'all',\n            'avoid-page',\n            'page',\n            'left',\n            'right',\n            'column'\n        ];\n    const scalePosition = ()=>[\n            'center',\n            'top',\n            'bottom',\n            'left',\n            'right',\n            'top-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-top',\n            'top-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-top',\n            'bottom-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-bottom',\n            'bottom-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-bottom'\n        ];\n    const scalePositionWithArbitrary = ()=>[\n            ...scalePosition(),\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleOverflow = ()=>[\n            'auto',\n            'hidden',\n            'clip',\n            'visible',\n            'scroll'\n        ];\n    const scaleOverscroll = ()=>[\n            'auto',\n            'contain',\n            'none'\n        ];\n    const scaleUnambiguousSpacing = ()=>[\n            isArbitraryVariable,\n            isArbitraryValue,\n            themeSpacing\n        ];\n    const scaleInset = ()=>[\n            isFraction,\n            'full',\n            'auto',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleGridTemplateColsRows = ()=>[\n            isInteger,\n            'none',\n            'subgrid',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartAndEnd = ()=>[\n            'auto',\n            {\n                span: [\n                    'full',\n                    isInteger,\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartOrEnd = ()=>[\n            isInteger,\n            'auto',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridAutoColsRows = ()=>[\n            'auto',\n            'min',\n            'max',\n            'fr',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleAlignPrimaryAxis = ()=>[\n            'start',\n            'end',\n            'center',\n            'between',\n            'around',\n            'evenly',\n            'stretch',\n            'baseline',\n            'center-safe',\n            'end-safe'\n        ];\n    const scaleAlignSecondaryAxis = ()=>[\n            'start',\n            'end',\n            'center',\n            'stretch',\n            'center-safe',\n            'end-safe'\n        ];\n    const scaleMargin = ()=>[\n            'auto',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleSizing = ()=>[\n            isFraction,\n            'auto',\n            'full',\n            'dvw',\n            'dvh',\n            'lvw',\n            'lvh',\n            'svw',\n            'svh',\n            'min',\n            'max',\n            'fit',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleColor = ()=>[\n            themeColor,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBgPosition = ()=>[\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            {\n                position: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleBgRepeat = ()=>[\n            'no-repeat',\n            {\n                repeat: [\n                    '',\n                    'x',\n                    'y',\n                    'space',\n                    'round'\n                ]\n            }\n        ];\n    const scaleBgSize = ()=>[\n            'auto',\n            'cover',\n            'contain',\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            {\n                size: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleGradientStopPosition = ()=>[\n            isPercent,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleRadius = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            'full',\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBorderWidth = ()=>[\n            '',\n            isNumber,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleLineStyle = ()=>[\n            'solid',\n            'dashed',\n            'dotted',\n            'double'\n        ];\n    const scaleBlendMode = ()=>[\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity'\n        ];\n    const scaleMaskImagePosition = ()=>[\n            isNumber,\n            isPercent,\n            isArbitraryVariablePosition,\n            isArbitraryPosition\n        ];\n    const scaleBlur = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleRotate = ()=>[\n            'none',\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleScale = ()=>[\n            'none',\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleSkew = ()=>[\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleTranslate = ()=>[\n            isFraction,\n            'full',\n            ...scaleUnambiguousSpacing()\n        ];\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: [\n                'spin',\n                'ping',\n                'pulse',\n                'bounce'\n            ],\n            aspect: [\n                'video'\n            ],\n            blur: [\n                isTshirtSize\n            ],\n            breakpoint: [\n                isTshirtSize\n            ],\n            color: [\n                isAny\n            ],\n            container: [\n                isTshirtSize\n            ],\n            'drop-shadow': [\n                isTshirtSize\n            ],\n            ease: [\n                'in',\n                'out',\n                'in-out'\n            ],\n            font: [\n                isAnyNonArbitrary\n            ],\n            'font-weight': [\n                'thin',\n                'extralight',\n                'light',\n                'normal',\n                'medium',\n                'semibold',\n                'bold',\n                'extrabold',\n                'black'\n            ],\n            'inset-shadow': [\n                isTshirtSize\n            ],\n            leading: [\n                'none',\n                'tight',\n                'snug',\n                'normal',\n                'relaxed',\n                'loose'\n            ],\n            perspective: [\n                'dramatic',\n                'near',\n                'normal',\n                'midrange',\n                'distant',\n                'none'\n            ],\n            radius: [\n                isTshirtSize\n            ],\n            shadow: [\n                isTshirtSize\n            ],\n            spacing: [\n                'px',\n                isNumber\n            ],\n            text: [\n                isTshirtSize\n            ],\n            'text-shadow': [\n                isTshirtSize\n            ],\n            tracking: [\n                'tighter',\n                'tight',\n                'normal',\n                'wide',\n                'wider',\n                'widest'\n            ]\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n            /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */ aspect: [\n                {\n                    aspect: [\n                        'auto',\n                        'square',\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect\n                    ]\n                }\n            ],\n            /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */ container: [\n                'container'\n            ],\n            /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */ columns: [\n                {\n                    columns: [\n                        isNumber,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeContainer\n                    ]\n                }\n            ],\n            /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */ 'break-after': [\n                {\n                    'break-after': scaleBreak()\n                }\n            ],\n            /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */ 'break-before': [\n                {\n                    'break-before': scaleBreak()\n                }\n            ],\n            /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */ 'break-inside': [\n                {\n                    'break-inside': [\n                        'auto',\n                        'avoid',\n                        'avoid-page',\n                        'avoid-column'\n                    ]\n                }\n            ],\n            /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */ 'box-decoration': [\n                {\n                    'box-decoration': [\n                        'slice',\n                        'clone'\n                    ]\n                }\n            ],\n            /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */ box: [\n                {\n                    box: [\n                        'border',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */ display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden'\n            ],\n            /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */ sr: [\n                'sr-only',\n                'not-sr-only'\n            ],\n            /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */ float: [\n                {\n                    float: [\n                        'right',\n                        'left',\n                        'none',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */ clear: [\n                {\n                    clear: [\n                        'left',\n                        'right',\n                        'both',\n                        'none',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */ isolation: [\n                'isolate',\n                'isolation-auto'\n            ],\n            /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */ 'object-fit': [\n                {\n                    object: [\n                        'contain',\n                        'cover',\n                        'fill',\n                        'none',\n                        'scale-down'\n                    ]\n                }\n            ],\n            /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */ 'object-position': [\n                {\n                    object: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */ overflow: [\n                {\n                    overflow: scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */ 'overflow-x': [\n                {\n                    'overflow-x': scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */ 'overflow-y': [\n                {\n                    'overflow-y': scaleOverflow()\n                }\n            ],\n            /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ overscroll: [\n                {\n                    overscroll: scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ 'overscroll-x': [\n                {\n                    'overscroll-x': scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ 'overscroll-y': [\n                {\n                    'overscroll-y': scaleOverscroll()\n                }\n            ],\n            /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */ position: [\n                'static',\n                'fixed',\n                'absolute',\n                'relative',\n                'sticky'\n            ],\n            /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ inset: [\n                {\n                    inset: scaleInset()\n                }\n            ],\n            /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ 'inset-x': [\n                {\n                    'inset-x': scaleInset()\n                }\n            ],\n            /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ 'inset-y': [\n                {\n                    'inset-y': scaleInset()\n                }\n            ],\n            /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ start: [\n                {\n                    start: scaleInset()\n                }\n            ],\n            /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ end: [\n                {\n                    end: scaleInset()\n                }\n            ],\n            /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ top: [\n                {\n                    top: scaleInset()\n                }\n            ],\n            /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ right: [\n                {\n                    right: scaleInset()\n                }\n            ],\n            /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ bottom: [\n                {\n                    bottom: scaleInset()\n                }\n            ],\n            /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ left: [\n                {\n                    left: scaleInset()\n                }\n            ],\n            /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */ visibility: [\n                'visible',\n                'invisible',\n                'collapse'\n            ],\n            /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */ z: [\n                {\n                    z: [\n                        isInteger,\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n            /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */ basis: [\n                {\n                    basis: [\n                        isFraction,\n                        'full',\n                        'auto',\n                        themeContainer,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */ 'flex-direction': [\n                {\n                    flex: [\n                        'row',\n                        'row-reverse',\n                        'col',\n                        'col-reverse'\n                    ]\n                }\n            ],\n            /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */ 'flex-wrap': [\n                {\n                    flex: [\n                        'nowrap',\n                        'wrap',\n                        'wrap-reverse'\n                    ]\n                }\n            ],\n            /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */ flex: [\n                {\n                    flex: [\n                        isNumber,\n                        isFraction,\n                        'auto',\n                        'initial',\n                        'none',\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */ grow: [\n                {\n                    grow: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */ shrink: [\n                {\n                    shrink: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */ order: [\n                {\n                    order: [\n                        isInteger,\n                        'first',\n                        'last',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */ 'grid-cols': [\n                {\n                    'grid-cols': scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-start-end': [\n                {\n                    col: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-start': [\n                {\n                    'col-start': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-end': [\n                {\n                    'col-end': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */ 'grid-rows': [\n                {\n                    'grid-rows': scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-start-end': [\n                {\n                    row: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-start': [\n                {\n                    'row-start': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-end': [\n                {\n                    'row-end': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */ 'grid-flow': [\n                {\n                    'grid-flow': [\n                        'row',\n                        'col',\n                        'dense',\n                        'row-dense',\n                        'col-dense'\n                    ]\n                }\n            ],\n            /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */ 'auto-cols': [\n                {\n                    'auto-cols': scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */ 'auto-rows': [\n                {\n                    'auto-rows': scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */ gap: [\n                {\n                    gap: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */ 'gap-x': [\n                {\n                    'gap-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */ 'gap-y': [\n                {\n                    'gap-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */ 'justify-content': [\n                {\n                    justify: [\n                        ...scaleAlignPrimaryAxis(),\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */ 'justify-items': [\n                {\n                    'justify-items': [\n                        ...scaleAlignSecondaryAxis(),\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */ 'justify-self': [\n                {\n                    'justify-self': [\n                        'auto',\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */ 'align-content': [\n                {\n                    content: [\n                        'normal',\n                        ...scaleAlignPrimaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */ 'align-items': [\n                {\n                    items: [\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                '',\n                                'last'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */ 'align-self': [\n                {\n                    self: [\n                        'auto',\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                '',\n                                'last'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */ 'place-content': [\n                {\n                    'place-content': scaleAlignPrimaryAxis()\n                }\n            ],\n            /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */ 'place-items': [\n                {\n                    'place-items': [\n                        ...scaleAlignSecondaryAxis(),\n                        'baseline'\n                    ]\n                }\n            ],\n            /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */ 'place-self': [\n                {\n                    'place-self': [\n                        'auto',\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            // Spacing\n            /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */ p: [\n                {\n                    p: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */ px: [\n                {\n                    px: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */ py: [\n                {\n                    py: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */ ps: [\n                {\n                    ps: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */ pe: [\n                {\n                    pe: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */ pt: [\n                {\n                    pt: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */ pr: [\n                {\n                    pr: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */ pb: [\n                {\n                    pb: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */ pl: [\n                {\n                    pl: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */ m: [\n                {\n                    m: scaleMargin()\n                }\n            ],\n            /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */ mx: [\n                {\n                    mx: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */ my: [\n                {\n                    my: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */ ms: [\n                {\n                    ms: scaleMargin()\n                }\n            ],\n            /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */ me: [\n                {\n                    me: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */ mt: [\n                {\n                    mt: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */ mr: [\n                {\n                    mr: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */ mb: [\n                {\n                    mb: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */ ml: [\n                {\n                    ml: scaleMargin()\n                }\n            ],\n            /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-x': [\n                {\n                    'space-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-x-reverse': [\n                'space-x-reverse'\n            ],\n            /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-y': [\n                {\n                    'space-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-y-reverse': [\n                'space-y-reverse'\n            ],\n            // --------------\n            // --- Sizing ---\n            // --------------\n            /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */ size: [\n                {\n                    size: scaleSizing()\n                }\n            ],\n            /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */ w: [\n                {\n                    w: [\n                        themeContainer,\n                        'screen',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */ 'min-w': [\n                {\n                    'min-w': [\n                        themeContainer,\n                        'screen',\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'none',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */ 'max-w': [\n                {\n                    'max-w': [\n                        themeContainer,\n                        'screen',\n                        'none',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'prose',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ {\n                            screen: [\n                                themeBreakpoint\n                            ]\n                        },\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */ h: [\n                {\n                    h: [\n                        'screen',\n                        'lh',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */ 'min-h': [\n                {\n                    'min-h': [\n                        'screen',\n                        'lh',\n                        'none',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */ 'max-h': [\n                {\n                    'max-h': [\n                        'screen',\n                        'lh',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Typography ---\n            // ------------------\n            /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */ 'font-size': [\n                {\n                    text: [\n                        'base',\n                        themeText,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */ 'font-smoothing': [\n                'antialiased',\n                'subpixel-antialiased'\n            ],\n            /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */ 'font-style': [\n                'italic',\n                'not-italic'\n            ],\n            /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */ 'font-weight': [\n                {\n                    font: [\n                        themeFontWeight,\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */ 'font-stretch': [\n                {\n                    'font-stretch': [\n                        'ultra-condensed',\n                        'extra-condensed',\n                        'condensed',\n                        'semi-condensed',\n                        'normal',\n                        'semi-expanded',\n                        'expanded',\n                        'extra-expanded',\n                        'ultra-expanded',\n                        isPercent,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */ 'font-family': [\n                {\n                    font: [\n                        isArbitraryVariableFamilyName,\n                        isArbitraryValue,\n                        themeFont\n                    ]\n                }\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-normal': [\n                'normal-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-ordinal': [\n                'ordinal'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-slashed-zero': [\n                'slashed-zero'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-figure': [\n                'lining-nums',\n                'oldstyle-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-spacing': [\n                'proportional-nums',\n                'tabular-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-fraction': [\n                'diagonal-fractions',\n                'stacked-fractions'\n            ],\n            /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */ tracking: [\n                {\n                    tracking: [\n                        themeTracking,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */ 'line-clamp': [\n                {\n                    'line-clamp': [\n                        isNumber,\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */ leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ themeLeading,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */ 'list-image': [\n                {\n                    'list-image': [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */ 'list-style-position': [\n                {\n                    list: [\n                        'inside',\n                        'outside'\n                    ]\n                }\n            ],\n            /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */ 'list-style-type': [\n                {\n                    list: [\n                        'disc',\n                        'decimal',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */ 'text-alignment': [\n                {\n                    text: [\n                        'left',\n                        'center',\n                        'right',\n                        'justify',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */ 'placeholder-color': [\n                {\n                    placeholder: scaleColor()\n                }\n            ],\n            /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */ 'text-color': [\n                {\n                    text: scaleColor()\n                }\n            ],\n            /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */ 'text-decoration': [\n                'underline',\n                'overline',\n                'line-through',\n                'no-underline'\n            ],\n            /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */ 'text-decoration-style': [\n                {\n                    decoration: [\n                        ...scaleLineStyle(),\n                        'wavy'\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */ 'text-decoration-thickness': [\n                {\n                    decoration: [\n                        isNumber,\n                        'from-font',\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */ 'text-decoration-color': [\n                {\n                    decoration: scaleColor()\n                }\n            ],\n            /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */ 'underline-offset': [\n                {\n                    'underline-offset': [\n                        isNumber,\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */ 'text-transform': [\n                'uppercase',\n                'lowercase',\n                'capitalize',\n                'normal-case'\n            ],\n            /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */ 'text-overflow': [\n                'truncate',\n                'text-ellipsis',\n                'text-clip'\n            ],\n            /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */ 'text-wrap': [\n                {\n                    text: [\n                        'wrap',\n                        'nowrap',\n                        'balance',\n                        'pretty'\n                    ]\n                }\n            ],\n            /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */ indent: [\n                {\n                    indent: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */ 'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */ whitespace: [\n                {\n                    whitespace: [\n                        'normal',\n                        'nowrap',\n                        'pre',\n                        'pre-line',\n                        'pre-wrap',\n                        'break-spaces'\n                    ]\n                }\n            ],\n            /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */ break: [\n                {\n                    break: [\n                        'normal',\n                        'words',\n                        'all',\n                        'keep'\n                    ]\n                }\n            ],\n            /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */ wrap: [\n                {\n                    wrap: [\n                        'break-word',\n                        'anywhere',\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */ hyphens: [\n                {\n                    hyphens: [\n                        'none',\n                        'manual',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */ content: [\n                {\n                    content: [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n            /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */ 'bg-attachment': [\n                {\n                    bg: [\n                        'fixed',\n                        'local',\n                        'scroll'\n                    ]\n                }\n            ],\n            /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */ 'bg-clip': [\n                {\n                    'bg-clip': [\n                        'border',\n                        'padding',\n                        'content',\n                        'text'\n                    ]\n                }\n            ],\n            /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */ 'bg-origin': [\n                {\n                    'bg-origin': [\n                        'border',\n                        'padding',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */ 'bg-position': [\n                {\n                    bg: scaleBgPosition()\n                }\n            ],\n            /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */ 'bg-repeat': [\n                {\n                    bg: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */ 'bg-size': [\n                {\n                    bg: scaleBgSize()\n                }\n            ],\n            /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */ 'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        {\n                            linear: [\n                                {\n                                    to: [\n                                        't',\n                                        'tr',\n                                        'r',\n                                        'br',\n                                        'b',\n                                        'bl',\n                                        'l',\n                                        'tl'\n                                    ]\n                                },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            radial: [\n                                '',\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            conic: [\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ]\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage\n                    ]\n                }\n            ],\n            /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */ 'bg-color': [\n                {\n                    bg: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-from-pos': [\n                {\n                    from: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-via-pos': [\n                {\n                    via: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-to-pos': [\n                {\n                    to: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-from': [\n                {\n                    from: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-via': [\n                {\n                    via: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-to': [\n                {\n                    to: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Borders ---\n            // ---------------\n            /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */ rounded: [\n                {\n                    rounded: scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-s': [\n                {\n                    'rounded-s': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-e': [\n                {\n                    'rounded-e': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-t': [\n                {\n                    'rounded-t': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-r': [\n                {\n                    'rounded-r': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-b': [\n                {\n                    'rounded-b': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-l': [\n                {\n                    'rounded-l': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-ss': [\n                {\n                    'rounded-ss': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-se': [\n                {\n                    'rounded-se': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-ee': [\n                {\n                    'rounded-ee': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-es': [\n                {\n                    'rounded-es': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-tl': [\n                {\n                    'rounded-tl': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-tr': [\n                {\n                    'rounded-tr': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-br': [\n                {\n                    'rounded-br': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-bl': [\n                {\n                    'rounded-bl': scaleRadius()\n                }\n            ],\n            /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w': [\n                {\n                    border: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-x': [\n                {\n                    'border-x': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-y': [\n                {\n                    'border-y': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-s': [\n                {\n                    'border-s': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-e': [\n                {\n                    'border-e': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-t': [\n                {\n                    'border-t': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-r': [\n                {\n                    'border-r': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-b': [\n                {\n                    'border-b': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-l': [\n                {\n                    'border-l': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-x': [\n                {\n                    'divide-x': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-x-reverse': [\n                'divide-x-reverse'\n            ],\n            /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-y': [\n                {\n                    'divide-y': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-y-reverse': [\n                'divide-y-reverse'\n            ],\n            /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */ 'border-style': [\n                {\n                    border: [\n                        ...scaleLineStyle(),\n                        'hidden',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */ 'divide-style': [\n                {\n                    divide: [\n                        ...scaleLineStyle(),\n                        'hidden',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color': [\n                {\n                    border: scaleColor()\n                }\n            ],\n            /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-x': [\n                {\n                    'border-x': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-y': [\n                {\n                    'border-y': scaleColor()\n                }\n            ],\n            /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-s': [\n                {\n                    'border-s': scaleColor()\n                }\n            ],\n            /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-e': [\n                {\n                    'border-e': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-t': [\n                {\n                    'border-t': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-r': [\n                {\n                    'border-r': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-b': [\n                {\n                    'border-b': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-l': [\n                {\n                    'border-l': scaleColor()\n                }\n            ],\n            /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */ 'divide-color': [\n                {\n                    divide: scaleColor()\n                }\n            ],\n            /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */ 'outline-style': [\n                {\n                    outline: [\n                        ...scaleLineStyle(),\n                        'none',\n                        'hidden'\n                    ]\n                }\n            ],\n            /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */ 'outline-offset': [\n                {\n                    'outline-offset': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */ 'outline-w': [\n                {\n                    outline: [\n                        '',\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */ 'outline-color': [\n                {\n                    outline: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Effects ---\n            // ---------------\n            /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */ shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */ 'shadow-color': [\n                {\n                    shadow: scaleColor()\n                }\n            ],\n            /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */ 'inset-shadow': [\n                {\n                    'inset-shadow': [\n                        'none',\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */ 'inset-shadow-color': [\n                {\n                    'inset-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */ 'ring-w': [\n                {\n                    ring: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-w-inset': [\n                'ring-inset'\n            ],\n            /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */ 'ring-color': [\n                {\n                    ring: scaleColor()\n                }\n            ],\n            /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-offset-w': [\n                {\n                    'ring-offset': [\n                        isNumber,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-offset-color': [\n                {\n                    'ring-offset': scaleColor()\n                }\n            ],\n            /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */ 'inset-ring-w': [\n                {\n                    'inset-ring': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */ 'inset-ring-color': [\n                {\n                    'inset-ring': scaleColor()\n                }\n            ],\n            /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */ 'text-shadow': [\n                {\n                    'text-shadow': [\n                        'none',\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */ 'text-shadow-color': [\n                {\n                    'text-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */ opacity: [\n                {\n                    opacity: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */ 'mix-blend': [\n                {\n                    'mix-blend': [\n                        ...scaleBlendMode(),\n                        'plus-darker',\n                        'plus-lighter'\n                    ]\n                }\n            ],\n            /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */ 'bg-blend': [\n                {\n                    'bg-blend': scaleBlendMode()\n                }\n            ],\n            /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */ 'mask-clip': [\n                {\n                    'mask-clip': [\n                        'border',\n                        'padding',\n                        'content',\n                        'fill',\n                        'stroke',\n                        'view'\n                    ]\n                },\n                'mask-no-clip'\n            ],\n            /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */ 'mask-composite': [\n                {\n                    mask: [\n                        'add',\n                        'subtract',\n                        'intersect',\n                        'exclude'\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ 'mask-image-linear-pos': [\n                {\n                    'mask-linear': [\n                        isNumber\n                    ]\n                }\n            ],\n            'mask-image-linear-from-pos': [\n                {\n                    'mask-linear-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-linear-to-pos': [\n                {\n                    'mask-linear-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-linear-from-color': [\n                {\n                    'mask-linear-from': scaleColor()\n                }\n            ],\n            'mask-image-linear-to-color': [\n                {\n                    'mask-linear-to': scaleColor()\n                }\n            ],\n            'mask-image-t-from-pos': [\n                {\n                    'mask-t-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-t-to-pos': [\n                {\n                    'mask-t-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-t-from-color': [\n                {\n                    'mask-t-from': scaleColor()\n                }\n            ],\n            'mask-image-t-to-color': [\n                {\n                    'mask-t-to': scaleColor()\n                }\n            ],\n            'mask-image-r-from-pos': [\n                {\n                    'mask-r-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-r-to-pos': [\n                {\n                    'mask-r-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-r-from-color': [\n                {\n                    'mask-r-from': scaleColor()\n                }\n            ],\n            'mask-image-r-to-color': [\n                {\n                    'mask-r-to': scaleColor()\n                }\n            ],\n            'mask-image-b-from-pos': [\n                {\n                    'mask-b-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-b-to-pos': [\n                {\n                    'mask-b-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-b-from-color': [\n                {\n                    'mask-b-from': scaleColor()\n                }\n            ],\n            'mask-image-b-to-color': [\n                {\n                    'mask-b-to': scaleColor()\n                }\n            ],\n            'mask-image-l-from-pos': [\n                {\n                    'mask-l-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-l-to-pos': [\n                {\n                    'mask-l-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-l-from-color': [\n                {\n                    'mask-l-from': scaleColor()\n                }\n            ],\n            'mask-image-l-to-color': [\n                {\n                    'mask-l-to': scaleColor()\n                }\n            ],\n            'mask-image-x-from-pos': [\n                {\n                    'mask-x-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-x-to-pos': [\n                {\n                    'mask-x-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-x-from-color': [\n                {\n                    'mask-x-from': scaleColor()\n                }\n            ],\n            'mask-image-x-to-color': [\n                {\n                    'mask-x-to': scaleColor()\n                }\n            ],\n            'mask-image-y-from-pos': [\n                {\n                    'mask-y-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-y-to-pos': [\n                {\n                    'mask-y-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-y-from-color': [\n                {\n                    'mask-y-from': scaleColor()\n                }\n            ],\n            'mask-image-y-to-color': [\n                {\n                    'mask-y-to': scaleColor()\n                }\n            ],\n            'mask-image-radial': [\n                {\n                    'mask-radial': [\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            'mask-image-radial-from-pos': [\n                {\n                    'mask-radial-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-radial-to-pos': [\n                {\n                    'mask-radial-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-radial-from-color': [\n                {\n                    'mask-radial-from': scaleColor()\n                }\n            ],\n            'mask-image-radial-to-color': [\n                {\n                    'mask-radial-to': scaleColor()\n                }\n            ],\n            'mask-image-radial-shape': [\n                {\n                    'mask-radial': [\n                        'circle',\n                        'ellipse'\n                    ]\n                }\n            ],\n            'mask-image-radial-size': [\n                {\n                    'mask-radial': [\n                        {\n                            closest: [\n                                'side',\n                                'corner'\n                            ],\n                            farthest: [\n                                'side',\n                                'corner'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            'mask-image-radial-pos': [\n                {\n                    'mask-radial-at': scalePosition()\n                }\n            ],\n            'mask-image-conic-pos': [\n                {\n                    'mask-conic': [\n                        isNumber\n                    ]\n                }\n            ],\n            'mask-image-conic-from-pos': [\n                {\n                    'mask-conic-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-conic-to-pos': [\n                {\n                    'mask-conic-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-conic-from-color': [\n                {\n                    'mask-conic-from': scaleColor()\n                }\n            ],\n            'mask-image-conic-to-color': [\n                {\n                    'mask-conic-to': scaleColor()\n                }\n            ],\n            /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */ 'mask-mode': [\n                {\n                    mask: [\n                        'alpha',\n                        'luminance',\n                        'match'\n                    ]\n                }\n            ],\n            /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */ 'mask-origin': [\n                {\n                    'mask-origin': [\n                        'border',\n                        'padding',\n                        'content',\n                        'fill',\n                        'stroke',\n                        'view'\n                    ]\n                }\n            ],\n            /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */ 'mask-position': [\n                {\n                    mask: scaleBgPosition()\n                }\n            ],\n            /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */ 'mask-repeat': [\n                {\n                    mask: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */ 'mask-size': [\n                {\n                    mask: scaleBgSize()\n                }\n            ],\n            /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */ 'mask-type': [\n                {\n                    'mask-type': [\n                        'alpha',\n                        'luminance'\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ 'mask-image': [\n                {\n                    mask: [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ---------------\n            // --- Filters ---\n            // ---------------\n            /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */ filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */ blur: [\n                {\n                    blur: scaleBlur()\n                }\n            ],\n            /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */ brightness: [\n                {\n                    brightness: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */ contrast: [\n                {\n                    contrast: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */ 'drop-shadow': [\n                {\n                    'drop-shadow': [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */ 'drop-shadow-color': [\n                {\n                    'drop-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */ grayscale: [\n                {\n                    grayscale: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */ 'hue-rotate': [\n                {\n                    'hue-rotate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */ invert: [\n                {\n                    invert: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */ saturate: [\n                {\n                    saturate: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */ sepia: [\n                {\n                    sepia: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */ 'backdrop-filter': [\n                {\n                    'backdrop-filter': [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */ 'backdrop-blur': [\n                {\n                    'backdrop-blur': scaleBlur()\n                }\n            ],\n            /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */ 'backdrop-brightness': [\n                {\n                    'backdrop-brightness': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */ 'backdrop-contrast': [\n                {\n                    'backdrop-contrast': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */ 'backdrop-grayscale': [\n                {\n                    'backdrop-grayscale': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */ 'backdrop-hue-rotate': [\n                {\n                    'backdrop-hue-rotate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */ 'backdrop-invert': [\n                {\n                    'backdrop-invert': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */ 'backdrop-opacity': [\n                {\n                    'backdrop-opacity': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */ 'backdrop-saturate': [\n                {\n                    'backdrop-saturate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */ 'backdrop-sepia': [\n                {\n                    'backdrop-sepia': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // --------------\n            // --- Tables ---\n            // --------------\n            /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */ 'border-collapse': [\n                {\n                    border: [\n                        'collapse',\n                        'separate'\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing': [\n                {\n                    'border-spacing': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing-x': [\n                {\n                    'border-spacing-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing-y': [\n                {\n                    'border-spacing-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */ 'table-layout': [\n                {\n                    table: [\n                        'auto',\n                        'fixed'\n                    ]\n                }\n            ],\n            /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */ caption: [\n                {\n                    caption: [\n                        'top',\n                        'bottom'\n                    ]\n                }\n            ],\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n            /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */ transition: [\n                {\n                    transition: [\n                        '',\n                        'all',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */ 'transition-behavior': [\n                {\n                    transition: [\n                        'normal',\n                        'discrete'\n                    ]\n                }\n            ],\n            /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */ duration: [\n                {\n                    duration: [\n                        isNumber,\n                        'initial',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */ ease: [\n                {\n                    ease: [\n                        'linear',\n                        'initial',\n                        themeEase,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */ delay: [\n                {\n                    delay: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */ animate: [\n                {\n                    animate: [\n                        'none',\n                        themeAnimate,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n            /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */ backface: [\n                {\n                    backface: [\n                        'hidden',\n                        'visible'\n                    ]\n                }\n            ],\n            /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */ perspective: [\n                {\n                    perspective: [\n                        themePerspective,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */ 'perspective-origin': [\n                {\n                    'perspective-origin': scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */ rotate: [\n                {\n                    rotate: scaleRotate()\n                }\n            ],\n            /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-x': [\n                {\n                    'rotate-x': scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-y': [\n                {\n                    'rotate-y': scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-z': [\n                {\n                    'rotate-z': scaleRotate()\n                }\n            ],\n            /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */ scale: [\n                {\n                    scale: scaleScale()\n                }\n            ],\n            /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-x': [\n                {\n                    'scale-x': scaleScale()\n                }\n            ],\n            /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-y': [\n                {\n                    'scale-y': scaleScale()\n                }\n            ],\n            /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-z': [\n                {\n                    'scale-z': scaleScale()\n                }\n            ],\n            /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-3d': [\n                'scale-3d'\n            ],\n            /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */ skew: [\n                {\n                    skew: scaleSkew()\n                }\n            ],\n            /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */ 'skew-x': [\n                {\n                    'skew-x': scaleSkew()\n                }\n            ],\n            /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */ 'skew-y': [\n                {\n                    'skew-y': scaleSkew()\n                }\n            ],\n            /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */ transform: [\n                {\n                    transform: [\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                        '',\n                        'none',\n                        'gpu',\n                        'cpu'\n                    ]\n                }\n            ],\n            /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */ 'transform-origin': [\n                {\n                    origin: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */ 'transform-style': [\n                {\n                    transform: [\n                        '3d',\n                        'flat'\n                    ]\n                }\n            ],\n            /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */ translate: [\n                {\n                    translate: scaleTranslate()\n                }\n            ],\n            /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-x': [\n                {\n                    'translate-x': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-y': [\n                {\n                    'translate-y': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-z': [\n                {\n                    'translate-z': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-none': [\n                'translate-none'\n            ],\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n            /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */ accent: [\n                {\n                    accent: scaleColor()\n                }\n            ],\n            /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */ appearance: [\n                {\n                    appearance: [\n                        'none',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */ 'caret-color': [\n                {\n                    caret: scaleColor()\n                }\n            ],\n            /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */ 'color-scheme': [\n                {\n                    scheme: [\n                        'normal',\n                        'dark',\n                        'light',\n                        'light-dark',\n                        'only-dark',\n                        'only-light'\n                    ]\n                }\n            ],\n            /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */ cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */ 'field-sizing': [\n                {\n                    'field-sizing': [\n                        'fixed',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */ 'pointer-events': [\n                {\n                    'pointer-events': [\n                        'auto',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */ resize: [\n                {\n                    resize: [\n                        'none',\n                        '',\n                        'y',\n                        'x'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */ 'scroll-behavior': [\n                {\n                    scroll: [\n                        'auto',\n                        'smooth'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-m': [\n                {\n                    'scroll-m': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mx': [\n                {\n                    'scroll-mx': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-my': [\n                {\n                    'scroll-my': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-ms': [\n                {\n                    'scroll-ms': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-me': [\n                {\n                    'scroll-me': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mt': [\n                {\n                    'scroll-mt': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mr': [\n                {\n                    'scroll-mr': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mb': [\n                {\n                    'scroll-mb': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-ml': [\n                {\n                    'scroll-ml': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-p': [\n                {\n                    'scroll-p': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-px': [\n                {\n                    'scroll-px': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-py': [\n                {\n                    'scroll-py': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-ps': [\n                {\n                    'scroll-ps': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pe': [\n                {\n                    'scroll-pe': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pt': [\n                {\n                    'scroll-pt': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pr': [\n                {\n                    'scroll-pr': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pb': [\n                {\n                    'scroll-pb': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pl': [\n                {\n                    'scroll-pl': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */ 'snap-align': [\n                {\n                    snap: [\n                        'start',\n                        'end',\n                        'center',\n                        'align-none'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */ 'snap-stop': [\n                {\n                    snap: [\n                        'normal',\n                        'always'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ 'snap-type': [\n                {\n                    snap: [\n                        'none',\n                        'x',\n                        'y',\n                        'both'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ 'snap-strictness': [\n                {\n                    snap: [\n                        'mandatory',\n                        'proximity'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */ touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'manipulation'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-x': [\n                {\n                    'touch-pan': [\n                        'x',\n                        'left',\n                        'right'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-y': [\n                {\n                    'touch-pan': [\n                        'y',\n                        'up',\n                        'down'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-pz': [\n                'touch-pinch-zoom'\n            ],\n            /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */ select: [\n                {\n                    select: [\n                        'none',\n                        'text',\n                        'all',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */ 'will-change': [\n                {\n                    'will-change': [\n                        'auto',\n                        'scroll',\n                        'contents',\n                        'transform',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -----------\n            // --- SVG ---\n            // -----------\n            /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */ fill: [\n                {\n                    fill: [\n                        'none',\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */ 'stroke-w': [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */ stroke: [\n                {\n                    stroke: [\n                        'none',\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n            /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */ 'forced-color-adjust': [\n                {\n                    'forced-color-adjust': [\n                        'auto',\n                        'none'\n                    ]\n                }\n            ]\n        },\n        conflictingClassGroups: {\n            overflow: [\n                'overflow-x',\n                'overflow-y'\n            ],\n            overscroll: [\n                'overscroll-x',\n                'overscroll-y'\n            ],\n            inset: [\n                'inset-x',\n                'inset-y',\n                'start',\n                'end',\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ],\n            'inset-x': [\n                'right',\n                'left'\n            ],\n            'inset-y': [\n                'top',\n                'bottom'\n            ],\n            flex: [\n                'basis',\n                'grow',\n                'shrink'\n            ],\n            gap: [\n                'gap-x',\n                'gap-y'\n            ],\n            p: [\n                'px',\n                'py',\n                'ps',\n                'pe',\n                'pt',\n                'pr',\n                'pb',\n                'pl'\n            ],\n            px: [\n                'pr',\n                'pl'\n            ],\n            py: [\n                'pt',\n                'pb'\n            ],\n            m: [\n                'mx',\n                'my',\n                'ms',\n                'me',\n                'mt',\n                'mr',\n                'mb',\n                'ml'\n            ],\n            mx: [\n                'mr',\n                'ml'\n            ],\n            my: [\n                'mt',\n                'mb'\n            ],\n            size: [\n                'w',\n                'h'\n            ],\n            'font-size': [\n                'leading'\n            ],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction'\n            ],\n            'fvn-ordinal': [\n                'fvn-normal'\n            ],\n            'fvn-slashed-zero': [\n                'fvn-normal'\n            ],\n            'fvn-figure': [\n                'fvn-normal'\n            ],\n            'fvn-spacing': [\n                'fvn-normal'\n            ],\n            'fvn-fraction': [\n                'fvn-normal'\n            ],\n            'line-clamp': [\n                'display',\n                'overflow'\n            ],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl'\n            ],\n            'rounded-s': [\n                'rounded-ss',\n                'rounded-es'\n            ],\n            'rounded-e': [\n                'rounded-se',\n                'rounded-ee'\n            ],\n            'rounded-t': [\n                'rounded-tl',\n                'rounded-tr'\n            ],\n            'rounded-r': [\n                'rounded-tr',\n                'rounded-br'\n            ],\n            'rounded-b': [\n                'rounded-br',\n                'rounded-bl'\n            ],\n            'rounded-l': [\n                'rounded-tl',\n                'rounded-bl'\n            ],\n            'border-spacing': [\n                'border-spacing-x',\n                'border-spacing-y'\n            ],\n            'border-w': [\n                'border-w-x',\n                'border-w-y',\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l'\n            ],\n            'border-w-x': [\n                'border-w-r',\n                'border-w-l'\n            ],\n            'border-w-y': [\n                'border-w-t',\n                'border-w-b'\n            ],\n            'border-color': [\n                'border-color-x',\n                'border-color-y',\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l'\n            ],\n            'border-color-x': [\n                'border-color-r',\n                'border-color-l'\n            ],\n            'border-color-y': [\n                'border-color-t',\n                'border-color-b'\n            ],\n            translate: [\n                'translate-x',\n                'translate-y',\n                'translate-none'\n            ],\n            'translate-none': [\n                'translate',\n                'translate-x',\n                'translate-y',\n                'translate-z'\n            ],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml'\n            ],\n            'scroll-mx': [\n                'scroll-mr',\n                'scroll-ml'\n            ],\n            'scroll-my': [\n                'scroll-mt',\n                'scroll-mb'\n            ],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl'\n            ],\n            'scroll-px': [\n                'scroll-pr',\n                'scroll-pl'\n            ],\n            'scroll-py': [\n                'scroll-pt',\n                'scroll-pb'\n            ],\n            touch: [\n                'touch-x',\n                'touch-y',\n                'touch-pz'\n            ],\n            'touch-x': [\n                'touch'\n            ],\n            'touch-y': [\n                'touch'\n            ],\n            'touch-pz': [\n                'touch'\n            ]\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': [\n                'leading'\n            ]\n        },\n        orderSensitiveModifiers: [\n            '*',\n            '**',\n            'after',\n            'backdrop',\n            'before',\n            'details-content',\n            'file',\n            'first-letter',\n            'first-line',\n            'marker',\n            'placeholder',\n            'selection'\n        ]\n    };\n};\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */ const mergeConfigs = (baseConfig, param)=>{\n    let { cacheSize, prefix, experimentalParseClassName, extend = {}, override = {} } = param;\n    overrideProperty(baseConfig, 'cacheSize', cacheSize);\n    overrideProperty(baseConfig, 'prefix', prefix);\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n    overrideConfigProperties(baseConfig.theme, override.theme);\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n    mergeConfigProperties(baseConfig.theme, extend.theme);\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n    return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue)=>{\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue;\n    }\n};\nconst overrideConfigProperties = (baseObject, overrideObject)=>{\n    if (overrideObject) {\n        for(const key in overrideObject){\n            overrideProperty(baseObject, key, overrideObject[key]);\n        }\n    }\n};\nconst mergeConfigProperties = (baseObject, mergeObject)=>{\n    if (mergeObject) {\n        for(const key in mergeObject){\n            mergeArrayProperties(baseObject, mergeObject, key);\n        }\n    }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key)=>{\n    const mergeValue = mergeObject[key];\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n    }\n};\nconst extendTailwindMerge = function(configExtension) {\n    for(var _len = arguments.length, createConfig = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        createConfig[_key - 1] = arguments[_key];\n    }\n    return typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(()=>mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\n};\nconst twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);\n //# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/error.jsx":
/*!***************************!*\
  !*** ./src/app/error.jsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/UnifiedButton */ \"(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Error(param) {\n    let { error, reset } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            // Bezpieczne logowanie błędu do konsoli\n            try {\n                if (error) {\n                    console.warn(\"--- Błąd Aplikacji Next.js ---\");\n                    if (error.message) {\n                        console.warn(\"Wiadomość:\", error.message);\n                    }\n                    if (error.stack) {\n                        console.warn(\"Stos wywołań:\", error.stack);\n                    }\n                }\n            } catch (loggingError) {\n            // Ignoruj błędy logowania\n            }\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"py-section-lg min-h-screen bg-gradient-to-b from-sanctuary via-sand-light/50 to-ocean/10 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-container-sm text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-5xl md:text-6xl font-display text-charcoal tracking-tight mb-md\",\n                    children: \"Ups!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-display text-charcoal mb-sm\",\n                    children: \"Coś poszło nie tak\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-charcoal-light/80 mb-lg\",\n                    children: \"Przepraszamy, wystąpił nieoczekiwany błąd. Nasz zesp\\xf3ł został powiadomiony.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-sm justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>reset(),\n                            className: \"inline-flex items-center justify-center px-hero-padding py-3 bg-charcoal text-sanctuary rectangular hover:bg-charcoal/90 transition-colors elegant-border\",\n                            children: \"Spr\\xf3buj ponownie\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center justify-center px-hero-padding py-3 bg-sanctuary text-charcoal border border-charcoal/20 rectangular hover:bg-sanctuary/80 transition-colors elegant-border elegant-border-hover\",\n                            children: \"Wr\\xf3ć na stronę gł\\xf3wną\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\error.jsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_s(Error, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Error;\nvar _c;\n$RefreshReg$(_c, \"Error\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/error.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/UnifiedButton.jsx":
/*!*********************************************!*\
  !*** ./src/components/ui/UnifiedButton.jsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CTAButton: () => (/* binding */ CTAButton),\n/* harmony export */   GhostButton: () => (/* binding */ GhostButton),\n/* harmony export */   LinkButton: () => (/* binding */ LinkButton),\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton),\n/* harmony export */   \"default\": () => (/* binding */ UnifiedButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default,CTAButton,SecondaryButton,GhostButton,LinkButton auto */ \n\n\n/**\r\n * UnifiedButton - Ujednolicony system przycisków BAKASANA\r\n * Elegancja Old Money + Ciepły minimalizm\r\n */ const buttonVariants = {\n    // PRIMARY - Główne akcje (CTA)\n    primary: {\n        base: \"bg-enterprise-brown text-sanctuary border border-enterprise-brown\",\n        hover: \"hover:bg-terra hover:border-terra hover:shadow-elegant\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2\"\n    },\n    // SECONDARY - Drugie w hierarchii\n    secondary: {\n        base: \"bg-transparent text-enterprise-brown border border-enterprise-brown\",\n        hover: \"hover:bg-enterprise-brown hover:text-sanctuary hover:shadow-elegant\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2\"\n    },\n    // GHOST - Subtelne akcje\n    ghost: {\n        base: \"bg-transparent text-charcoal border-0\",\n        hover: \"hover:bg-whisper hover:text-enterprise-brown\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1\"\n    },\n    // MINIMAL - Ultra-subtelne\n    minimal: {\n        base: \"bg-transparent text-sage border-0 underline decoration-1 underline-offset-4\",\n        hover: \"hover:text-enterprise-brown hover:decoration-enterprise-brown\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1\"\n    }\n};\nconst sizeVariants = {\n    // All sizes meet WCAG 2.1 AA touch target requirements (44px minimum)\n    sm: \"px-6 py-3 text-xs tracking-[1px] min-h-[44px]\",\n    md: \"px-8 py-3.5 text-sm tracking-[1.2px] min-h-[48px]\",\n    lg: \"px-12 py-4 text-sm tracking-[1.5px] min-h-[52px]\",\n    xl: \"px-16 py-5 text-base tracking-[2px] min-h-[56px]\" // 56px height\n};\nfunction UnifiedButton(param) {\n    let { children, variant = 'primary', size = 'md', className = '', disabled = false, loading = false, as: Component = 'button', ...props } = param;\n    const variantStyles = buttonVariants[variant];\n    const sizeStyles = sizeVariants[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(// Base styles - Old Money elegance\n        \"inline-flex items-center justify-center font-inter font-light uppercase\", \"transition-all duration-300 ease-out\", \"focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\", \"transform hover:-translate-y-0.5 active:translate-y-0\", // Variant styles\n        variantStyles.base, variantStyles.hover, variantStyles.focus, // Size styles\n        sizeStyles, // Loading state\n        loading && \"opacity-70 cursor-wait\", // Custom className\n        className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = UnifiedButton;\n// Wyspecjalizowane warianty dla częstych przypadków użycia\nfunction CTAButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"primary\",\n        size: \"lg\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CTAButton;\nfunction SecondaryButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"secondary\",\n        size: \"md\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SecondaryButton;\nfunction GhostButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"ghost\",\n        size: \"md\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_c3 = GhostButton;\nfunction LinkButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"minimal\",\n        size: \"sm\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_c4 = LinkButton;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UnifiedButton\");\n$RefreshReg$(_c1, \"CTAButton\");\n$RefreshReg$(_c2, \"SecondaryButton\");\n$RefreshReg$(_c3, \"GhostButton\");\n$RefreshReg$(_c4, \"LinkButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1VuaWZpZWRCdXR0b24uanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDUTtBQUVsQzs7O0NBR0MsR0FFRCxNQUFNRSxpQkFBaUI7SUFDckIsK0JBQStCO0lBQy9CQyxTQUFTO1FBQ1BDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7SUFFQSxrQ0FBa0M7SUFDbENDLFdBQVc7UUFDVEgsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE9BQU87SUFDVDtJQUVBLHlCQUF5QjtJQUN6QkUsT0FBTztRQUNMSixNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztJQUNUO0lBRUEsMkJBQTJCO0lBQzNCRyxTQUFTO1FBQ1BMLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE1BQU1JLGVBQWU7SUFDbkIsc0VBQXNFO0lBQ3RFQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJLG1EQUF1RCxjQUFjO0FBQzNFO0FBRWUsU0FBU0MsY0FBYyxLQVNyQztRQVRxQyxFQUNwQ0MsUUFBUSxFQUNSQyxVQUFVLFNBQVMsRUFDbkJDLE9BQU8sSUFBSSxFQUNYQyxZQUFZLEVBQUUsRUFDZEMsV0FBVyxLQUFLLEVBQ2hCQyxVQUFVLEtBQUssRUFDZkMsSUFBSUMsWUFBWSxRQUFRLEVBQ3hCLEdBQUdDLE9BQ0osR0FUcUM7SUFVcEMsTUFBTUMsZ0JBQWdCdkIsY0FBYyxDQUFDZSxRQUFRO0lBQzdDLE1BQU1TLGFBQWFoQixZQUFZLENBQUNRLEtBQUs7SUFFckMscUJBQ0UsOERBQUNLO1FBQ0NKLFdBQVdsQiw4Q0FBRUEsQ0FDWCxtQ0FBbUM7UUFDbkMsMkVBQ0Esd0NBQ0Esc0VBQ0EseURBRUEsaUJBQWlCO1FBQ2pCd0IsY0FBY3JCLElBQUksRUFDbEJxQixjQUFjcEIsS0FBSyxFQUNuQm9CLGNBQWNuQixLQUFLLEVBRW5CLGNBQWM7UUFDZG9CLFlBRUEsZ0JBQWdCO1FBQ2hCTCxXQUFXLDBCQUVYLG1CQUFtQjtRQUNuQkY7UUFFRkMsVUFBVUEsWUFBWUM7UUFDckIsR0FBR0csS0FBSzs7WUFFUkgseUJBQ0MsOERBQUNNO2dCQUNDUixXQUFVO2dCQUNWUyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0NYLFdBQVU7d0JBQ1ZZLElBQUc7d0JBQ0hDLElBQUc7d0JBQ0hDLEdBQUU7d0JBQ0ZDLFFBQU87d0JBQ1BDLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ0M7d0JBQ0NqQixXQUFVO3dCQUNWUyxNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFJUHJCOzs7Ozs7O0FBR1A7S0EvRHdCRDtBQWlFeEIsMkRBQTJEO0FBRXBELFNBQVN1QixVQUFVLEtBQXNCO1FBQXRCLEVBQUV0QixRQUFRLEVBQUUsR0FBR1EsT0FBTyxHQUF0QjtJQUN4QixxQkFDRSw4REFBQ1Q7UUFBY0UsU0FBUTtRQUFVQyxNQUFLO1FBQU0sR0FBR00sS0FBSztrQkFDakRSOzs7Ozs7QUFHUDtNQU5nQnNCO0FBUVQsU0FBU0MsZ0JBQWdCLEtBQXNCO1FBQXRCLEVBQUV2QixRQUFRLEVBQUUsR0FBR1EsT0FBTyxHQUF0QjtJQUM5QixxQkFDRSw4REFBQ1Q7UUFBY0UsU0FBUTtRQUFZQyxNQUFLO1FBQU0sR0FBR00sS0FBSztrQkFDbkRSOzs7Ozs7QUFHUDtNQU5nQnVCO0FBUVQsU0FBU0MsWUFBWSxLQUFzQjtRQUF0QixFQUFFeEIsUUFBUSxFQUFFLEdBQUdRLE9BQU8sR0FBdEI7SUFDMUIscUJBQ0UsOERBQUNUO1FBQWNFLFNBQVE7UUFBUUMsTUFBSztRQUFNLEdBQUdNLEtBQUs7a0JBQy9DUjs7Ozs7O0FBR1A7TUFOZ0J3QjtBQVFULFNBQVNDLFdBQVcsS0FBc0I7UUFBdEIsRUFBRXpCLFFBQVEsRUFBRSxHQUFHUSxPQUFPLEdBQXRCO0lBQ3pCLHFCQUNFLDhEQUFDVDtRQUFjRSxTQUFRO1FBQVVDLE1BQUs7UUFBTSxHQUFHTSxLQUFLO2tCQUNqRFI7Ozs7OztBQUdQO01BTmdCeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxVbmlmaWVkQnV0dG9uLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBjbiAgfSBmcm9tICdAL2xpYi91dGlscyc7XHJcblxyXG4vKipcclxuICogVW5pZmllZEJ1dHRvbiAtIFVqZWRub2xpY29ueSBzeXN0ZW0gcHJ6eWNpc2vDs3cgQkFLQVNBTkFcclxuICogRWxlZ2FuY2phIE9sZCBNb25leSArIENpZXDFgnkgbWluaW1hbGl6bVxyXG4gKi9cclxuXHJcbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0ge1xyXG4gIC8vIFBSSU1BUlkgLSBHxYLDs3duZSBha2NqZSAoQ1RBKVxyXG4gIHByaW1hcnk6IHtcclxuICAgIGJhc2U6IFwiYmctZW50ZXJwcmlzZS1icm93biB0ZXh0LXNhbmN0dWFyeSBib3JkZXIgYm9yZGVyLWVudGVycHJpc2UtYnJvd25cIixcclxuICAgIGhvdmVyOiBcImhvdmVyOmJnLXRlcnJhIGhvdmVyOmJvcmRlci10ZXJyYSBob3ZlcjpzaGFkb3ctZWxlZ2FudFwiLFxyXG4gICAgZm9jdXM6IFwiZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW50ZXJwcmlzZS1icm93bi8yMCBmb2N1czpyaW5nLW9mZnNldC0yXCJcclxuICB9LFxyXG4gIFxyXG4gIC8vIFNFQ09OREFSWSAtIERydWdpZSB3IGhpZXJhcmNoaWlcclxuICBzZWNvbmRhcnk6IHtcclxuICAgIGJhc2U6IFwiYmctdHJhbnNwYXJlbnQgdGV4dC1lbnRlcnByaXNlLWJyb3duIGJvcmRlciBib3JkZXItZW50ZXJwcmlzZS1icm93blwiLFxyXG4gICAgaG92ZXI6IFwiaG92ZXI6YmctZW50ZXJwcmlzZS1icm93biBob3Zlcjp0ZXh0LXNhbmN0dWFyeSBob3ZlcjpzaGFkb3ctZWxlZ2FudFwiLFxyXG4gICAgZm9jdXM6IFwiZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW50ZXJwcmlzZS1icm93bi8yMCBmb2N1czpyaW5nLW9mZnNldC0yXCJcclxuICB9LFxyXG4gIFxyXG4gIC8vIEdIT1NUIC0gU3VidGVsbmUgYWtjamVcclxuICBnaG9zdDoge1xyXG4gICAgYmFzZTogXCJiZy10cmFuc3BhcmVudCB0ZXh0LWNoYXJjb2FsIGJvcmRlci0wXCIsXHJcbiAgICBob3ZlcjogXCJob3ZlcjpiZy13aGlzcGVyIGhvdmVyOnRleHQtZW50ZXJwcmlzZS1icm93blwiLFxyXG4gICAgZm9jdXM6IFwiZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW50ZXJwcmlzZS1icm93bi8xMCBmb2N1czpyaW5nLW9mZnNldC0xXCJcclxuICB9LFxyXG4gIFxyXG4gIC8vIE1JTklNQUwgLSBVbHRyYS1zdWJ0ZWxuZVxyXG4gIG1pbmltYWw6IHtcclxuICAgIGJhc2U6IFwiYmctdHJhbnNwYXJlbnQgdGV4dC1zYWdlIGJvcmRlci0wIHVuZGVybGluZSBkZWNvcmF0aW9uLTEgdW5kZXJsaW5lLW9mZnNldC00XCIsXHJcbiAgICBob3ZlcjogXCJob3Zlcjp0ZXh0LWVudGVycHJpc2UtYnJvd24gaG92ZXI6ZGVjb3JhdGlvbi1lbnRlcnByaXNlLWJyb3duXCIsXHJcbiAgICBmb2N1czogXCJmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1lbnRlcnByaXNlLWJyb3duLzEwIGZvY3VzOnJpbmctb2Zmc2V0LTFcIlxyXG4gIH1cclxufTtcclxuXHJcbmNvbnN0IHNpemVWYXJpYW50cyA9IHtcclxuICAvLyBBbGwgc2l6ZXMgbWVldCBXQ0FHIDIuMSBBQSB0b3VjaCB0YXJnZXQgcmVxdWlyZW1lbnRzICg0NHB4IG1pbmltdW0pXHJcbiAgc206IFwicHgtNiBweS0zIHRleHQteHMgdHJhY2tpbmctWzFweF0gbWluLWgtWzQ0cHhdXCIsICAgICAgICAvLyA0NHB4IGhlaWdodFxyXG4gIG1kOiBcInB4LTggcHktMy41IHRleHQtc20gdHJhY2tpbmctWzEuMnB4XSBtaW4taC1bNDhweF1cIiwgICAvLyA0OHB4IGhlaWdodFxyXG4gIGxnOiBcInB4LTEyIHB5LTQgdGV4dC1zbSB0cmFja2luZy1bMS41cHhdIG1pbi1oLVs1MnB4XVwiLCAgICAvLyA1MnB4IGhlaWdodFxyXG4gIHhsOiBcInB4LTE2IHB5LTUgdGV4dC1iYXNlIHRyYWNraW5nLVsycHhdIG1pbi1oLVs1NnB4XVwiICAgICAvLyA1NnB4IGhlaWdodFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVW5pZmllZEJ1dHRvbih7XHJcbiAgY2hpbGRyZW4sXHJcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcclxuICBzaXplID0gJ21kJyxcclxuICBjbGFzc05hbWUgPSAnJyxcclxuICBkaXNhYmxlZCA9IGZhbHNlLFxyXG4gIGxvYWRpbmcgPSBmYWxzZSxcclxuICBhczogQ29tcG9uZW50ID0gJ2J1dHRvbicsXHJcbiAgLi4ucHJvcHNcclxufSkge1xyXG4gIGNvbnN0IHZhcmlhbnRTdHlsZXMgPSBidXR0b25WYXJpYW50c1t2YXJpYW50XTtcclxuICBjb25zdCBzaXplU3R5bGVzID0gc2l6ZVZhcmlhbnRzW3NpemVdO1xyXG4gIFxyXG4gIHJldHVybiAoXHJcbiAgICA8Q29tcG9uZW50XHJcbiAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgLy8gQmFzZSBzdHlsZXMgLSBPbGQgTW9uZXkgZWxlZ2FuY2VcclxuICAgICAgICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmb250LWludGVyIGZvbnQtbGlnaHQgdXBwZXJjYXNlXCIsXHJcbiAgICAgICAgXCJ0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1vdXRcIixcclxuICAgICAgICBcImZvY3VzOm91dGxpbmUtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiLFxyXG4gICAgICAgIFwidHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0wLjUgYWN0aXZlOnRyYW5zbGF0ZS15LTBcIixcclxuICAgICAgICBcclxuICAgICAgICAvLyBWYXJpYW50IHN0eWxlc1xyXG4gICAgICAgIHZhcmlhbnRTdHlsZXMuYmFzZSxcclxuICAgICAgICB2YXJpYW50U3R5bGVzLmhvdmVyLFxyXG4gICAgICAgIHZhcmlhbnRTdHlsZXMuZm9jdXMsXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU2l6ZSBzdHlsZXNcclxuICAgICAgICBzaXplU3R5bGVzLFxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIExvYWRpbmcgc3RhdGVcclxuICAgICAgICBsb2FkaW5nICYmIFwib3BhY2l0eS03MCBjdXJzb3Itd2FpdFwiLFxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIEN1c3RvbSBjbGFzc05hbWVcclxuICAgICAgICBjbGFzc05hbWVcclxuICAgICAgKX1cclxuICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkIHx8IGxvYWRpbmd9XHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgID5cclxuICAgICAge2xvYWRpbmcgJiYgKFxyXG4gICAgICAgIDxzdmcgXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gLW1sLTEgbXItMiBoLTQgdy00XCIgXHJcbiAgICAgICAgICBmaWxsPVwibm9uZVwiIFxyXG4gICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGNpcmNsZSBcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiIFxyXG4gICAgICAgICAgICBjeD1cIjEyXCIgXHJcbiAgICAgICAgICAgIGN5PVwiMTJcIiBcclxuICAgICAgICAgICAgcj1cIjEwXCIgXHJcbiAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIFxyXG4gICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxwYXRoIFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJvcGFjaXR5LTc1XCIgXHJcbiAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIiBcclxuICAgICAgICAgICAgZD1cIk00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvc3ZnPlxyXG4gICAgICApfVxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L0NvbXBvbmVudD5cclxuICApO1xyXG59XHJcblxyXG4vLyBXeXNwZWNqYWxpem93YW5lIHdhcmlhbnR5IGRsYSBjesSZc3R5Y2ggcHJ6eXBhZGvDs3cgdcW8eWNpYVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIENUQUJ1dHRvbih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxVbmlmaWVkQnV0dG9uIHZhcmlhbnQ9XCJwcmltYXJ5XCIgc2l6ZT1cImxnXCIgey4uLnByb3BzfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9VbmlmaWVkQnV0dG9uPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBTZWNvbmRhcnlCdXR0b24oeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8VW5pZmllZEJ1dHRvbiB2YXJpYW50PVwic2Vjb25kYXJ5XCIgc2l6ZT1cIm1kXCIgey4uLnByb3BzfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9VbmlmaWVkQnV0dG9uPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBHaG9zdEJ1dHRvbih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxVbmlmaWVkQnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJtZFwiIHsuLi5wcm9wc30+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvVW5pZmllZEJ1dHRvbj5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gTGlua0J1dHRvbih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxVbmlmaWVkQnV0dG9uIHZhcmlhbnQ9XCJtaW5pbWFsXCIgc2l6ZT1cInNtXCIgey4uLnByb3BzfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9VbmlmaWVkQnV0dG9uPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwicHJpbWFyeSIsImJhc2UiLCJob3ZlciIsImZvY3VzIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJtaW5pbWFsIiwic2l6ZVZhcmlhbnRzIiwic20iLCJtZCIsImxnIiwieGwiLCJVbmlmaWVkQnV0dG9uIiwiY2hpbGRyZW4iLCJ2YXJpYW50Iiwic2l6ZSIsImNsYXNzTmFtZSIsImRpc2FibGVkIiwibG9hZGluZyIsImFzIiwiQ29tcG9uZW50IiwicHJvcHMiLCJ2YXJpYW50U3R5bGVzIiwic2l6ZVN0eWxlcyIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwiQ1RBQnV0dG9uIiwiU2Vjb25kYXJ5QnV0dG9uIiwiR2hvc3RCdXR0b24iLCJMaW5rQnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.js":
/*!**************************!*\
  !*** ./src/lib/utils.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isInViewport: () => (/* binding */ isInViewport),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   lerp: () => (/* binding */ lerp),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_clsx_clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=clsx!=!clsx */ \"(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _barrel_optimize_names_twMerge_tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=twMerge!=!tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,_barrel_optimize_names_twMerge_tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,_barrel_optimize_names_clsx_clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Debounce function for performance optimization\r\n */ function debounce(func, wait) {\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}\n/**\r\n * Throttle function for performance optimization\r\n */ function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!inThrottle) {\n            func.apply(this, args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\r\n * Format price in Polish currency\r\n */ function formatPrice(price) {\n    return new Intl.NumberFormat('pl-PL', {\n        style: 'currency',\n        currency: 'PLN'\n    }).format(price);\n}\n/**\r\n * Validate email address\r\n */ function validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\r\n * Generate unique ID\r\n */ function generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n/**\r\n * Check if element is in viewport\r\n */ function isInViewport(element) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n}\n/**\r\n * Clamp number between min and max\r\n */ function clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n/**\r\n * Linear interpolation\r\n */ function lerp(start, end, factor) {\n    return start + (end - start) * factor;\n}\n/**\r\n * Check if device is mobile\r\n */ function isMobile() {\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n/**\r\n * Check if code is running in browser\r\n */ function isBrowser() {\n    return \"object\" !== 'undefined';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs":
/*!**************************************************************************!*\
  !*** __barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* reexport safe */ C_Users_david_Desktop_Projekty_bakasana_prod_bakasana_prod_node_modules_clsx_dist_clsx_mjs__WEBPACK_IMPORTED_MODULE_0__.clsx)\n/* harmony export */ });\n/* harmony import */ var C_Users_david_Desktop_Projekty_bakasana_prod_bakasana_prod_node_modules_clsx_dist_clsx_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/clsx/dist/clsx.mjs */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPWNsc3ghPSEuL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJha2FzYW5hX3Byb2RcXFxcYmFrYXNhbmFfcHJvZFxcXFxub2RlX21vZHVsZXNcXFxcY2xzeFxcXFxkaXN0XFxcXGNsc3gubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Cerror.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);