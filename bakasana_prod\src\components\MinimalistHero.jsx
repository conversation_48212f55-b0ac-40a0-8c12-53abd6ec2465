
'use client';
import Link from 'next/link';
import React, { useState, useEffect, useCallback } from 'react';

import { HeroTitle, SubTitle, StatNumber, StatLabel, Badge  } from '@/components/ui/UnifiedTypography';
import { SecondaryButton  } from '@/components/ui/UnifiedButton';

import PerformantWhatsApp from '@/components/PerformantWhatsApp';

import { useScrollReveal, useParallax, useReducedMotion  } from '@/hooks/useAdvancedAnimations';




const MinimalistHero = React.memo(function MinimalistHero() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  const prefersReducedMotion = useReducedMotion();
  const [heroRef] = useScrollReveal({ threshold: 0.2 });
  const [parallaxRef] = useParallax(0.3);


  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Enhanced mouse tracking for parallax effects
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const handleMouseMove = (e) => {
      const x = (e.clientX / window.innerWidth) * 100;
      const y = (e.clientY / window.innerHeight) * 100;
      setMousePosition({ x, y });
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [prefersReducedMotion]);

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        paddingTop: "120px",
        backgroundImage: "url('/images/background/bali-hero.webp')",
        backgroundSize: "cover",
        backgroundPosition: "center center",
        backgroundAttachment: "scroll",
        backgroundRepeat: "no-repeat",
        backgroundColor: "#FCF6EE"
      }}
    >
      {/* Enhanced parallax background layer - Fixed mobile performance */}
      <div
        ref={parallaxRef}
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: "url('/images/background/bali-hero.webp')",
          backgroundSize: "120%",
          backgroundPosition: `${50 + (mousePosition.x - 50) * 0.02}% ${50 + (mousePosition.y - 50) * 0.02}%`,
          backgroundAttachment: "scroll", // Changed from "fixed" to prevent mobile issues
          backgroundRepeat: "no-repeat",
          willChange: 'transform, background-position'
        }}
      />

      {/* Enterprise-level overlay gradient with mouse interaction */}
      <div 
        className="absolute inset-0 z-10"
        style={{
          background: `
            radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
              rgba(252,246,238,0.3) 0%, 
              rgba(255,255,255,0.6) 40%, 
              rgba(255,255,255,0.9) 100%
            ),
            linear-gradient(
              180deg,
              rgba(252,246,238,0.4) 0%,
              rgba(255,255,255,0.7) 60%,
              rgba(255,255,255,0.9) 100%
            )
          `,
          transition: prefersReducedMotion ? 'none' : 'background 0.3s ease-out'
        }}
      />

      {/* Subtle noise texture overlay for depth */}
      <div 
        className="absolute inset-0 z-10 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          mixBlendMode: 'multiply'
        }}
      />
      
      {/* Floating elements for depth */}
      <div 
        className="absolute inset-0 z-10 pointer-events-none"
        style={{
          transform: `translate(${(mousePosition.x - 50) * 0.05}px, ${(mousePosition.y - 50) * 0.05}px)`,
          transition: prefersReducedMotion ? 'none' : 'transform 0.3s ease-out'
        }}
      >
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-charcoal-gold rectangular opacity-30 animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-sand-amber rectangular opacity-40 animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-3/4 w-3 h-3 bg-charcoal-gold rectangular opacity-20 animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes staggeredFadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .fade-in-up {
          animation: fadeInUp 0.8s ease-out forwards;
        }
        
        .staggered-fade {
          animation: staggeredFadeIn 1s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
        }

        /* Custom selection color */
        ::selection {
          background-color: rgb(139, 115, 85);
          color: white;
        }
        
        ::-moz-selection {
          background-color: rgb(139, 115, 85);
          color: white;
        }
      `}</style>
      {/* Główna zawartość wycentrowana */}
      <div className={`relative z-10 text-center max-w-5xl mx-auto px-4 sm:px-6 lg:px-hero-padding transition-all duration-1000 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        
        {/* Badge - RETREATY JOGI (Enterprise refined) */}
        <div className={`inline-block mb-md transition-all duration-700 delay-200 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}>
          <Badge variant="outline" className="text-[11px] tracking-[3.5px]">
            RETREATY JOGI • BALI & SRI LANKA
          </Badge>
        </div>

        {/* Tytuł BAKASANA - Enterprise luxury */}
        <div className={`transition-all duration-1000 delay-300 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <HeroTitle>BAKASANA</HeroTitle>
        </div>

        {/* Podtytuł - Refined */}
        <div className={`transition-all duration-1000 delay-400 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <SubTitle>~ jóga jest drogą ciszy ~</SubTitle>
        </div>

        {/* Opis główny - Enterprise typography */}
        <p className={`text-sm sm:text-base lg:text-[17px] text-charcoal-light max-w-[620px] mx-auto leading-[1.85] font-normal mb-xl sm:mb-2xl transition-all duration-1000 delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
        </p>

        {/* Statystyki - Prawdziwe i autentyczne z mikrodata */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 md:gap-12 lg:gap-20 max-w-4xl mx-auto mb-xl sm:mb-2xl transition-all duration-1000 delay-600 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <div className="text-center" itemScope itemType="https://schema.org/EducationalOccupationalCredential">
            <StatNumber itemProp="credentialLevel">200h</StatNumber>
            <StatLabel itemProp="name">CERTYFIKACJA YTT</StatLabel>
          </div>
          <div className="text-center">
            <StatNumber>7</StatNumber>
            <StatLabel>LAT DOŚWIADCZENIA</StatLabel>
          </div>
          <div className="text-center">
            <StatNumber>150+</StatNumber>
            <StatLabel>ZADOWOLONYCH UCZESTNIKÓW</StatLabel>
          </div>
          <div className="text-center" itemScope itemType="https://schema.org/AggregateRating">
            <StatNumber itemProp="ratingValue">4.9</StatNumber>
            <StatLabel>ŚREDNIA OCEN</StatLabel>
            <meta itemProp="bestRating" content="5" />
            <meta itemProp="reviewCount" content="150" />
          </div>
        </div>

        {/* Przyciski CTA - Enhanced with WhatsApp */}
        <div className={`flex flex-col sm:flex-row gap-sm sm:gap-md justify-center items-center transition-all duration-1000 delay-700 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <SecondaryButton 
            as={Link}
            href="/program"
            size="lg"
            className="magnetic-element"
          >
            PRZEGLĄD HARMONOGRAMU
          </SecondaryButton>
          
          <PerformantWhatsApp 
            size="md"
            variant="button"
            className="inline-flex items-center font-light tracking-[2px] transition-all duration-500 hover:transform hover:-translate-y-0.5 focus:outline-none focus:opacity-70 magnetic-element px-12 py-4 text-sm"
          />
          
          <SecondaryButton 
            as={Link}
            href="/rezerwacja"
            size="lg"
            className="magnetic-element"
          >
            REZERWUJ KONSULTACJĘ
          </SecondaryButton>
        </div>
      </div>

      {/* Formularz boczny - Refined Enterprise */}
      <div className={`hidden xl:block absolute right-8 top-1/2 transform -translate-y-1/2 z-10 bg-pearl backdrop-blur-sm p-8 max-w-sm w-80 transition-all duration-1000 delay-800 border border-enterprise-brown/10 ${
        isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
      }`}
      style={{
        boxShadow: '0 10px 40px rgba(0,0,0,0.06)'
      }}>
        <h3 className="font-cormorant font-light text-charcoal leading-tight text-2xl tracking-[0.05em] mb-sm">Gotowa na transformację?</h3>
        <p className="text-sm text-sage mb-md">Rozpocznij swoją duchową podróż</p>
        <div className="space-y-sm">
          <input
            type="email"
            placeholder="Twój email"
            className="w-full px-0 py-3 border-0 border-b border-ash text-sm focus:outline-none focus:border-enterprise-brown transition-colors bg-transparent"
            disabled
          />
          <Link
            href="/kontakt"
            className="w-full px-hero-padding py-3 bg-enterprise-brown text-white font-light tracking-[2px] transition-all duration-300 hover:bg-terra flex items-center justify-center focus:outline-none focus:opacity-80"
            style={{ fontSize: '13px' }}
          >
            KONTAKT →
          </Link>
        </div>
      </div>
    </section>
  );
});


export default MinimalistHero;