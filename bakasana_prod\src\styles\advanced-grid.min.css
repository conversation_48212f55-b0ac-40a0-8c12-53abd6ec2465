@supports (container-type:inline-size){.grid-container{container-type:inline-size}}.grid-auto-fit{display:grid;grid-template-columns:repeat(auto-fit,minmax(var(--min-column-width,280px),1fr));gap:clamp(1rem,4vw,2rem);width:100%}.grid-auto-fill{display:grid;grid-template-columns:repeat(auto-fill,minmax(var(--min-column-width,280px),1fr));gap:clamp(1rem,4vw,2rem);width:100%}.grid-responsive-cards{--min-column-width:300px;display:grid;grid-template-columns:repeat(auto-fit,minmax(var(--min-column-width),1fr));gap:clamp(1.5rem,5vw,3rem);align-items:start}.grid-responsive-services{--min-column-width:280px;display:grid;grid-template-columns:repeat(auto-fit,minmax(var(--min-column-width),1fr));gap:clamp(2rem,6vw,4rem);align-items:stretch}.grid-responsive-testimonials{--min-column-width:320px;display:grid;grid-template-columns:repeat(auto-fit,minmax(var(--min-column-width),1fr));gap:clamp(1.5rem,4vw,2.5rem);align-items:start}.grid-responsive-gallery{--min-column-width:250px;display:grid;grid-template-columns:repeat(auto-fill,minmax(var(--min-column-width),1fr));gap:clamp(1rem,3vw,1.5rem);align-items:center}@supports (container-type:inline-size){.grid-container-responsive{container-type:inline-size;display:grid;gap:clamp(1rem,4vw,2rem)}@container (max-width:400px){.grid-container-responsive{grid-template-columns:1fr}}@container (min-width:401px) and (max-width:800px){.grid-container-responsive{grid-template-columns:repeat(2,1fr)}}@container (min-width:801px) and (max-width:1200px){.grid-container-responsive{grid-template-columns:repeat(3,1fr)}}@container (min-width:1201px){.grid-container-responsive{grid-template-columns:repeat(4,1fr)}}}.grid-masonry{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));grid-template-rows:masonry;gap:clamp(1rem,4vw,2rem);align-items:start}@supports not (grid-template-rows:masonry){.grid-masonry{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:clamp(1rem,4vw,2rem);align-items:start}}.grid-hero{display:grid;grid-template-columns:1fr;gap:clamp(2rem,8vw,4rem);align-items:center;min-height:100vh;padding:clamp(2rem,8vw,6rem) 0}@media (min-width:1024px){.grid-hero{grid-template-columns:1fr 1fr;gap:clamp(4rem,10vw,8rem)}}.grid-stats{display:grid;grid-template-columns:repeat(2,1fr);gap:clamp(1rem,4vw,2rem);text-align:center}@media (min-width:768px){.grid-stats{grid-template-columns:repeat(4,1fr);gap:clamp(2rem,6vw,4rem)}}.gap-responsive-sm{gap:clamp(.5rem,2vw,1rem)}.gap-responsive-md{gap:clamp(1rem,4vw,2rem)}.gap-responsive-lg{gap:clamp(1.5rem,6vw,3rem)}.gap-responsive-xl{gap:clamp(2rem,8vw,4rem)}.grid-items-start{align-items:start}.grid-items-center{align-items:center}.grid-items-end{align-items:end}.grid-items-stretch{align-items:stretch}.grid-content-start{justify-content:start}.grid-content-center{justify-content:center}.grid-content-end{justify-content:end}.grid-content-between{justify-content:space-between}@media (max-width:480px){.grid-auto-fit,.grid-auto-fill,.grid-responsive-cards,.grid-responsive-services,.grid-responsive-testimonials{grid-template-columns:1fr;gap:clamp(1rem,6vw,1.5rem)}}@media (min-width:481px) and (max-width:768px){.grid-responsive-services{grid-template-columns:repeat(auto-fit,minmax(250px,1fr))}.grid-responsive-testimonials{grid-template-columns:repeat(auto-fit,minmax(280px,1fr))}}@media (min-width:769px) and (max-width:1024px){.grid-responsive-cards{grid-template-columns:repeat(auto-fit,minmax(280px,1fr))}}@media (prefers-reduced-motion:reduce){.grid-auto-fit,.grid-auto-fill,.grid-responsive-cards,.grid-responsive-services,.grid-responsive-testimonials,.grid-responsive-gallery{transition:none}}@media (prefers-contrast:high){.grid-auto-fit,.grid-auto-fill,.grid-responsive-cards,.grid-responsive-services,.grid-responsive-testimonials,.grid-responsive-gallery{gap:clamp(1.5rem,5vw,3rem)}}@media print{.grid-auto-fit,.grid-auto-fill,.grid-responsive-cards,.grid-responsive-services,.grid-responsive-testimonials{grid-template-columns:repeat(2,1fr);gap:1rem;break-inside:avoid}}@supports (grid-template-rows:subgrid){.grid-subgrid-rows{grid-template-rows:subgrid}.grid-subgrid-columns{grid-template-columns:subgrid}}.grid-semantic{display:grid;grid-template-areas:"header header header" "sidebar main aside" "footer footer footer";grid-template-columns:200px 1fr 200px;grid-template-rows:auto 1fr auto;gap:clamp(1rem,4vw,2rem);min-height:100vh}@media (max-width:768px){.grid-semantic{grid-template-areas:"header" "main" "sidebar" "aside" "footer";grid-template-columns:1fr;grid-template-rows:auto auto auto auto auto}}