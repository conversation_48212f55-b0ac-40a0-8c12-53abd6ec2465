import dynamic from 'next/dynamic'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON> } from 'next/font/google'
import { Suspense } from 'react'

import CriticalCSS from '@/components/CriticalCSS'

import './globals.css'
import '../styles/microinteractions.css'
import '../styles/typography.css'
import '../styles/advanced-grid.css'
import '../styles/touch-targets.css'
import '../styles/advanced-typography.css'
import '../styles/premium-utilities.css'
import '../styles/navbar-fix.css'
import '../styles/navbar-critical-fix.css'
import ConditionalNavbar from '@/components/ConditionalNavbar'
import ContactStructuredData from '@/components/SEO/ContactStructuredData'
import CoreWebVitals from '@/components/CoreWebVitals'
import EnhancedStructuredData from '@/components/SEO/EnhancedStructuredData'
import Footer from '@/components/Footer'
import OptimizedBreadcrumbs from '@/components/OptimizedBreadcrumbs'
import QuickCTA from '@/components/QuickCTA'

import { generateAdvancedMetadata } from '@/lib/advancedSEO'

const LazyAnalytics = dynamic(() => import('@/components/Analytics/LazyAnalytics'), {
  loading: () => null
})
const PerformanceMonitor = dynamic(() => import('@/components/Performance/PerformanceMonitor'), {
  loading: () => null
})
import QualityAssuranceWrapper from '@/components/ui/QualityAssuranceWrapper'

import SmoothScrollProvider from '@/components/ui/SmoothScrollProvider'


const ClientOnlyResponsiveChecker = dynamic(() => import('@/components/ClientOnlyResponsiveChecker'), {
  loading: () => null
})


const cormorant = Cormorant_Garamond({
  subsets: ['latin'],
  weight: ['300', '400'],
  display: 'swap',
  variable: '--font-cormorant',
  preload: true
})

const inter = Inter({
  subsets: ['latin'],
  weight: ['300', '400'],
  display: 'swap',
  variable: '--font-inter',
  preload: true
})

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#fdfcf8' },
    { media: '(prefers-color-scheme: dark)', color: '#2c2c2c' }
  ]
}

export const metadata = {
  metadataBase: new URL('https://bakasana-travel.blog'),
  title: 'BAKASANA - Luksusowe Retreaty Jogi Bali & Sri Lanka 2025 | Premium Yoga Retreats ⭐',
  description: '🏆 Ekskluzywne wyjazdy wellness Azja z certyfikowaną instruktorką Julią Jakubowicz. ✈️ Premium yoga retreats spiritual journey 2025 | Luksusowe retreaty Ubud, Gili Air, Sigiriya | 4.9/5 ⭐ 127 opinii | Rezerwuj teraz!',

  keywords: 'luksusowe retreaty jogi bali sri lanka, ekskluzywne wyjazdy wellness azja, premium yoga retreats spiritual journey, retreaty jogi bali 2025, joga sri lanka, julia jakubowicz joga, yoga retreat polska, ubud yoga retreat, gili air joga, sigiriya yoga, transformacyjne podróże, medytacja bali, ayurveda sri lanka, joga wakacje azja, duchowa podróż bali, yoga teacher training, najlepsze retreaty jogi, certyfikowana instruktorka jogi, luxury wellness travel, ekskluzywne podróże duchowe, premium yoga experiences',
  authors: [{ name: 'Julia Jakubowicz', url: 'https://bakasana-travel.blog/o-mnie' }],
  creator: 'Julia Jakubowicz',
  publisher: 'BAKASANA',
  classification: 'Travel, Yoga, Wellness, Retreats',
  category: 'Yoga Retreats',
  applicationName: 'BAKASANA',
  generator: 'Next.js',
  referrer: 'origin-when-cross-origin',
  language: 'pl-PL',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    bing: process.env.BING_VERIFICATION,
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog',
    languages: {
      'pl-PL': 'https://bakasana-travel.blog',
      'en-US': 'https://bakasana-travel.blog/en',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    url: 'https://bakasana-travel.blog',
    siteName: 'BAKASANA - Premium Yoga Retreats',
    title: 'BAKASANA - Luksusowe Retreaty Jogi Bali & Sri Lanka 2025 ⭐',
    description: '🏆 Ekskluzywne wyjazdy wellness Azja z certyfikowaną instruktorką Julią Jakubowicz. Premium yoga retreats spiritual journey 2025',
    images: [
      {
        url: 'https://bakasana-travel.blog/images/og-image-main.jpg',
        width: 1200,
        height: 630,
        alt: 'BAKASANA - Premium Yoga Retreats Bali & Sri Lanka',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BAKASANA - Luksusowe Retreaty Jogi Bali & Sri Lanka 2025 ⭐',
    description: '🏆 Ekskluzywne wyjazdy wellness Azja z certyfikowaną instruktorką Julią Jakubowicz',
    images: ['https://bakasana-travel.blog/images/twitter-image.jpg'],
    creator: '@bakasana_travel',
    site: '@bakasana_travel',
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#8B4513' },
    ],
  },
  manifest: '/site.webmanifest',
  other: {
    'msapplication-TileColor': '#8B4513',
    'theme-color': '#8B4513',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="pl" className={`${inter.variable} ${cormorant.variable}`}>
      <head>
        <CriticalCSS />
      </head>
      <body className="antialiased">
        <QualityAssuranceWrapper>
          <SmoothScrollProvider>
            <ConditionalNavbar />
            <main className="min-h-screen">
              <OptimizedBreadcrumbs />
              {children}
              <QuickCTA />
            </main>
            <Footer />
            <Suspense fallback={null}>
              <LazyAnalytics />
            </Suspense>
            <Suspense fallback={null}>
              <PerformanceMonitor />
            </Suspense>
            <Suspense fallback={null}>
              <ClientOnlyResponsiveChecker />
            </Suspense>
            <CoreWebVitals />
            <ContactStructuredData />
            <EnhancedStructuredData />
          </SmoothScrollProvider>
        </QualityAssuranceWrapper>
      </body>
    </html>
  )
}
