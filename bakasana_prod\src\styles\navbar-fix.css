/* =============================================
   🔧 NAVBAR CONTRAST FIX - Desktop Navigation
   Ensures proper visibility and contrast for desktop navbar links
   ============================================= */

/* Desktop navbar link contrast enhancement */
@media (min-width: 1024px) {
  /* Base desktop navbar styles */
  .desktop-nav-link {
    color: #3A3633 !important; /* Dark color for better contrast */
    font-weight: 400 !important;
    opacity: 1 !important;
    text-shadow: none !important;
  }
  
  /* Hover state with gold color */
  .desktop-nav-link:hover {
    color: #D4AF37 !important; /* Gold hover color to match mobile */
    transform: translateY(-1px);
  }
  
  /* Active state */
  .desktop-nav-link.active {
    color: #D4AF37 !important; /* Gold for active state */
    font-weight: 500 !important;
  }
  
  /* Special styling for highlighted links */
  .desktop-nav-link.highlight {
    color: #8B7355 !important; /* Enterprise brown for special links */
    font-weight: 500 !important;
  }
  
  .desktop-nav-link.highlight:hover {
    color: #D4AF37 !important;
  }
}

/* Enhanced navbar background for better contrast */
@media (min-width: 1024px) {
  .navbar-scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(212, 175, 55, 0.15) !important;
    box-shadow: 0 2px 20px rgba(58, 54, 51, 0.08) !important;
  }
  
  .navbar-transparent {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }
}

/* Logo contrast enhancement */
@media (min-width: 1024px) {
  .navbar-logo {
    color: #3A3633 !important;
  }
  
  .navbar-logo:hover {
    color: #8B7355 !important;
  }
}

/* Mobile menu - for devices below 1024px */
@media (max-width: 1023px) {
  .mobile-nav-link {
    color: #3A3633 !important;
  }
  
  .mobile-nav-link:hover {
    color: #D4AF37 !important;
  }
  
  .mobile-nav-link.active {
    color: #D4AF37 !important;
  }
  

}

/* Accessibility enhancements */
@media (min-width: 1024px) {
  .desktop-nav-link:focus {
    outline: 2px solid #D4AF37 !important;
    outline-offset: 2px !important;
    color: #D4AF37 !important;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .desktop-nav-link {
      color: #000000 !important;
    }
    
    .desktop-nav-link:hover,
    .desktop-nav-link.active {
      color: #B8860B !important; /* Darker gold for high contrast */
    }
  }
}

/* Animation enhancements for better UX */
@media (min-width: 1024px) {
  .desktop-nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
  
  /* Underline animation for active/hover states */
  .nav-underline {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    opacity: 0;
    transform: scaleX(0);
    transition: all 0.3s ease;
  }
  
  .desktop-nav-link:hover .nav-underline,
  .desktop-nav-link.active .nav-underline {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* =============================================
   🚨 CRITICAL FIX - Force navbar visibility
   High specificity overrides to ensure navbar works
   ============================================= */

/* Force desktop menu visibility on desktop screens */
@media (min-width: 1024px) {
  nav .hidden.md\\:flex {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Hide mobile elements on desktop */
  nav .md\\:hidden {
    display: none !important;
  }
  
  /* Ensure WhatsApp button is visible on desktop */
  nav .hidden.md\\:block {
    display: block !important;
    visibility: visible !important;
  }
}

/* Force mobile menu visibility on mobile screens */
@media (max-width: 1023px) {
  nav .md\\:hidden {
    display: block !important;
    visibility: visible !important;
  }
  
  /* Hide desktop elements on mobile */
  nav .hidden.md\\:flex,
  nav .hidden.md\\:block {
    display: none !important;
  }
  
  /* Mobile menu container */
  nav .md\\:hidden.absolute {
    position: absolute !important;
  }
}

/* Additional safety overrides */
.navbar-logo {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure hamburger button works */
button[aria-label="Toggle menu"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (min-width: 1024px) {
  button[aria-label="Toggle menu"] {
    display: none !important;
  }
}