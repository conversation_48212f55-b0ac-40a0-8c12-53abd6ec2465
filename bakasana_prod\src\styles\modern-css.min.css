@supports (container-type:inline-size){.container-query{container-type:inline-size;container-name:main-container}.card-container{container-type:inline-size;container-name:card}.sidebar-container{container-type:inline-size;container-name:sidebar}@container main-container (max-width:400px){.container-responsive-text{font-size:clamp(.875rem,4vw,1rem);line-height:1.4}.container-responsive-heading{font-size:clamp(1.5rem,6vw,2rem);line-height:1.2}}@container main-container (min-width:401px) and (max-width:800px){.container-responsive-text{font-size:clamp(1rem,3vw,1.125rem);line-height:1.5}.container-responsive-heading{font-size:clamp(2rem,5vw,2.5rem);line-height:1.1}}@container main-container (min-width:801px){.container-responsive-text{font-size:clamp(1.125rem,2vw,1.25rem);line-height:1.6}.container-responsive-heading{font-size:clamp(2.5rem,4vw,3rem);line-height:1}}@container card (max-width:300px){.card-content{padding:1rem;flex-direction:column}.card-image{width:100%;height:150px}}@container card (min-width:301px) and (max-width:500px){.card-content{padding:1.5rem;flex-direction:column}.card-image{width:100%;height:200px}}@container card (min-width:501px){.card-content{padding:2rem;flex-direction:row;align-items:center}.card-image{width:40%;height:250px}}@container sidebar (max-width:200px){.sidebar-nav{flex-direction:column;gap:.5rem}.sidebar-nav-item{font-size:.875rem;padding:.5rem}}@container sidebar (min-width:201px){.sidebar-nav{flex-direction:column;gap:1rem}.sidebar-nav-item{font-size:1rem;padding:.75rem}}}@supports (grid-template-rows:subgrid){.grid-parent{display:grid;grid-template-columns:repeat(3,1fr);grid-template-rows:repeat(3,auto);gap:2rem}.grid-child-subgrid{display:grid;grid-column:span 2;grid-template-columns:subgrid;grid-template-rows:subgrid;gap:inherit}.card-grid-parent{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem}.card-with-subgrid{display:grid;grid-template-rows:auto 1fr auto}.card-grid-container{display:grid;grid-template-rows:subgrid;grid-row:span 3}}@supports (height:100dvh){.full-height-dynamic{height:100dvh;min-height:100dvh}.hero-dynamic{min-height:100dvh;display:flex;align-items:center;justify-content:center}}@supports (height:100lvh){.full-height-large{height:100lvh;min-height:100lvh}}@supports (height:100svh){.full-height-small{height:100svh;min-height:100svh}}@supports not (height:100dvh){.full-height-dynamic,.hero-dynamic{height:100vh;min-height:100vh}@media (max-width:768px){.full-height-dynamic,.hero-dynamic{height:calc(100vh - env(safe-area-inset-bottom));min-height:calc(100vh - env(safe-area-inset-bottom))}}}.logical-spacing{margin-block:2rem;margin-inline:1rem;padding-block:1rem;padding-inline:1.5rem;border-inline-start:3px solid var(--enterprise-brown)}.logical-text{text-align:start;border-inline-end:1px solid var(--stone);padding-inline-end:1rem}@supports (selector(&)){.nested-component{background:var(--sanctuary);padding:2rem;& .nested-title{color:var(--charcoal);margin-block-end:1rem;&+.nested-subtitle{color:var(--stone);font-size:.875rem}}& .nested-content{line-height:1.6;& p{margin-block-end:1rem;&+p{margin-block-start:.5rem}}}&:hover{background:var(--whisper);& .nested-title{color:var(--enterprise-brown)}}}}.advanced-custom-properties{--shadow-layers:0 1px 3px rgba(0,0,0,.1),0 4px 6px rgba(0,0,0,.05),0 10px 20px rgba(0,0,0,.03);--gradient-complex:linear-gradient(135deg,var(--sanctuary) 0%,var(--whisper) 25%,var(--linen) 50%,var(--sanctuary) 100%);--animation-sequence:fadeIn .6s ease-out,slideUp .4s ease-out .2s,scaleIn .3s ease-out .4s;box-shadow:var(--shadow-layers);background:var(--gradient-complex);animation:var(--animation-sequence)}@supports (animation-timeline:scroll()){.scroll-driven-animation{animation:fadeInOnScroll linear;animation-timeline:scroll();animation-range:entry 0% entry 100%}@keyframes fadeInOnScroll{from{opacity:0;transform:translateY(50px)}to{opacity:1;transform:translateY(0)}}.parallax-scroll{animation:parallaxMove linear;animation-timeline:scroll();animation-range:entry 0% exit 100%}@keyframes parallaxMove{from{transform:translateY(0)}to{transform:translateY(-100px)}}}@supports (anchor-name:--anchor){.anchor-element{anchor-name:--tooltip-anchor}.anchored-tooltip{position:absolute;position-anchor:--tooltip-anchor;bottom:anchor(top);left:anchor(center);transform:translateX(-50%);background:var(--charcoal);color:var(--sanctuary);padding:.5rem 1rem;border-radius:.25rem;font-size:.875rem;white-space:nowrap;z-index:1000}}@supports (grid-template-rows:masonry){.masonry-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));grid-template-rows:masonry;gap:2rem;align-tracks:start}.masonry-item{break-inside:avoid;margin-bottom:0}}@supports not (grid-template-rows:masonry){.masonry-grid{columns:300px;column-gap:2rem;column-fill:balance}.masonry-item{break-inside:avoid;margin-bottom:2rem;display:inline-block;width:100%}}.modern-css-base *{box-sizing:border-box}.modern-css-base body{margin:0;font-family:var(--font-secondary);line-height:1.6;color:var(--charcoal);background:var(--sanctuary)}.modern-btn{display:inline-flex;align-items:center;justify-content:center;padding:.75rem 1.5rem;border:1px solid transparent;font-weight:500;text-decoration:none;transition:all .3s ease;cursor:pointer}.modern-card{background:var(--sanctuary);border:1px solid var(--stone);padding:2rem;box-shadow:var(--shadow-subtle)}.modern-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.modern-focus-visible:focus-visible{outline:2px solid var(--enterprise-brown);outline-offset:2px}@supports (backdrop-filter:blur(10px)){.glass-effect{backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);background:rgba(253,252,248,.8)}}@supports (color:color(display-p3 1 0 0)){.wide-gamut-colors{color:color(display-p3 .8 .4 .2);background:color(display-p3 .98 .96 .94)}}@supports (font-variation-settings:normal){.variable-font{font-variation-settings:"wght" 400,"slnt" 0}.variable-font-bold{font-variation-settings:"wght" 700,"slnt" 0}}